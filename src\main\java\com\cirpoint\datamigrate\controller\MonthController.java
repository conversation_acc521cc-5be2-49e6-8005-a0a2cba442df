package com.cirpoint.datamigrate.controller;

import com.cirpoint.datamigrate.entity.MonthInfo;
import com.cirpoint.datamigrate.service.MonthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 月份管理控制器
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
@RestController
@RequestMapping("/api/months")
public class MonthController {
    
    @Autowired
    private MonthService monthService;
    
    /**
     * 扫描所有月份目录
     */
    @GetMapping("/scan")
    public ResponseEntity<Map<String, Object>> scanMonths() {
        try {
            List<MonthInfo> months = monthService.scanMonths();
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", months);
            result.put("count", months.size());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("扫描月份目录失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "扫描月份目录失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取可迁移的月份列表
     */
    @GetMapping("/migratable")
    public ResponseEntity<Map<String, Object>> getMigratableMonths() {
        try {
            List<MonthInfo> months = monthService.getMigratableMonths();
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", months);
            result.put("count", months.size());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取可迁移月份失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取可迁移月份失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取指定月份的详细信息
     */
    @GetMapping("/{month}")
    public ResponseEntity<Map<String, Object>> getMonthInfo(@PathVariable String month) {
        try {
            MonthInfo monthInfo = monthService.getMonthInfo(month);
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", monthInfo);
            return ResponseEntity.ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("月份格式错误: {}", e.getMessage());
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", e.getMessage());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取月份信息失败: " + month, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取月份信息失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取下个月份
     */
    @GetMapping("/next")
    public ResponseEntity<Map<String, Object>> getNextMonth() {
        try {
            String nextMonth = monthService.getNextMonth();
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", nextMonth);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取下个月份失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取下个月份失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取月份统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getMonthStatistics() {
        try {
            Map<String, Object> statistics = monthService.getMonthStatistics();
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", statistics);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取月份统计失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取月份统计失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 批量获取月份信息
     */
    @PostMapping("/batch")
    public ResponseEntity<Map<String, Object>> batchGetMonthInfo(@RequestBody List<String> months) {
        try {
            Map<String, MonthInfo> monthInfoMap = new HashMap<>();
            
            for (String month : months) {
                try {
                    MonthInfo monthInfo = monthService.getMonthInfo(month);
                    monthInfoMap.put(month, monthInfo);
                } catch (Exception e) {
                    log.warn("获取月份信息失败: " + month, e);
                    // 继续处理其他月份
                }
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", monthInfoMap);
            result.put("count", monthInfoMap.size());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("批量获取月份信息失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "批量获取月份信息失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 按状态过滤月份
     */
    @GetMapping("/filter/{status}")
    public ResponseEntity<Map<String, Object>> filterMonthsByStatus(@PathVariable String status) {
        try {
            List<MonthInfo> allMonths = monthService.scanMonths();
            
            MonthInfo.MigrateStatus targetStatus;
            try {
                targetStatus = MonthInfo.MigrateStatus.valueOf(status.toUpperCase());
            } catch (IllegalArgumentException e) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "无效的状态值: " + status);
                return ResponseEntity.ok(result);
            }
            
            List<MonthInfo> filteredMonths = allMonths.stream()
                    .filter(month -> month.getStatus() == targetStatus)
                    .collect(java.util.stream.Collectors.toList());
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", filteredMonths);
            result.put("count", filteredMonths.size());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("按状态过滤月份失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "按状态过滤月份失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取月份状态选项
     */
    @GetMapping("/statuses")
    public ResponseEntity<Map<String, Object>> getMonthStatuses() {
        try {
            Map<String, String> statuses = new HashMap<>();
            for (MonthInfo.MigrateStatus status : MonthInfo.MigrateStatus.values()) {
                statuses.put(status.name(), status.getName());
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", statuses);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取月份状态选项失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取月份状态选项失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
}
