package com.cirpoint.datamigrate.websocket;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 任务进度WebSocket服务
 * 用于实时推送任务执行进度和日志信息
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
@Component
@ServerEndpoint("/ws/task-progress/{taskId}")
public class TaskProgressWebSocket {
    
    /**
     * 存储所有连接的会话
     * key: taskId, value: Session集合
     */
    private static final Map<String, Map<String, Session>> TASK_SESSIONS = new ConcurrentHashMap<>();
    
    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("taskId") String taskId) {
        try {
            // 将会话添加到对应任务的会话集合中
            TASK_SESSIONS.computeIfAbsent(taskId, k -> new ConcurrentHashMap<>())
                    .put(session.getId(), session);
            
            log.info("WebSocket连接建立成功，任务ID: {}, 会话ID: {}", taskId, session.getId());
            
            // 发送连接成功消息
            sendMessage(session, createMessage("CONNECTED", "连接建立成功", null));
            
        } catch (Exception e) {
            log.error("WebSocket连接建立失败", e);
        }
    }
    
    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose(Session session, @PathParam("taskId") String taskId) {
        try {
            // 从会话集合中移除
            Map<String, Session> sessions = TASK_SESSIONS.get(taskId);
            if (sessions != null) {
                sessions.remove(session.getId());
                if (sessions.isEmpty()) {
                    TASK_SESSIONS.remove(taskId);
                }
            }
            
            log.info("WebSocket连接关闭，任务ID: {}, 会话ID: {}", taskId, session.getId());
            
        } catch (Exception e) {
            log.error("WebSocket连接关闭处理失败", e);
        }
    }
    
    /**
     * 收到客户端消息后调用的方法
     */
    @OnMessage
    public void onMessage(String message, Session session, @PathParam("taskId") String taskId) {
        try {
            log.debug("收到WebSocket消息，任务ID: {}, 会话ID: {}, 消息: {}", taskId, session.getId(), message);
            
            // 这里可以处理客户端发送的消息，比如暂停、取消任务等
            // 暂时只做日志记录
            
        } catch (Exception e) {
            log.error("处理WebSocket消息失败", e);
        }
    }
    
    /**
     * 发生错误时调用
     */
    @OnError
    public void onError(Session session, Throwable error, @PathParam("taskId") String taskId) {
        log.error("WebSocket发生错误，任务ID: {}, 会话ID: {}", taskId, session.getId(), error);
    }
    
    /**
     * 向指定任务的所有客户端发送消息
     */
    public static void sendToTask(String taskId, String type, String message, Object data) {
        Map<String, Session> sessions = TASK_SESSIONS.get(taskId);
        if (sessions == null || sessions.isEmpty()) {
            return;
        }
        
        String jsonMessage = createMessage(type, message, data);
        
        // 遍历所有会话，发送消息
        sessions.entrySet().removeIf(entry -> {
            Session session = entry.getValue();
            try {
                if (session.isOpen()) {
                    sendMessage(session, jsonMessage);
                    return false; // 保留会话
                } else {
                    return true; // 移除已关闭的会话
                }
            } catch (Exception e) {
                log.error("发送WebSocket消息失败，会话ID: {}", entry.getKey(), e);
                return true; // 移除出错的会话
            }
        });
    }
    
    /**
     * 发送任务进度更新
     */
    public static void sendProgress(String taskId, int totalProgress, int currentProgress, String currentMonth) {
        Map<String, Object> data = new ConcurrentHashMap<>();
        data.put("totalProgress", totalProgress);
        data.put("currentProgress", currentProgress);
        data.put("currentMonth", currentMonth);
        
        sendToTask(taskId, "PROGRESS", "进度更新", data);
    }
    
    /**
     * 发送任务状态更新
     */
    public static void sendStatus(String taskId, String status, String message) {
        Map<String, Object> data = new ConcurrentHashMap<>();
        data.put("status", status);
        
        sendToTask(taskId, "STATUS", message, data);
    }
    
    /**
     * 发送日志消息
     */
    public static void sendLog(String taskId, String logMessage) {
        sendToTask(taskId, "LOG", logMessage, null);
    }
    
    /**
     * 发送任务完成消息
     */
    public static void sendComplete(String taskId, boolean success, String result) {
        Map<String, Object> data = new ConcurrentHashMap<>();
        data.put("success", success);
        data.put("result", result);
        
        sendToTask(taskId, "COMPLETE", success ? "任务完成" : "任务失败", data);
    }
    
    /**
     * 向单个会话发送消息
     */
    private static void sendMessage(Session session, String message) {
        try {
            synchronized (session) {
                if (session.isOpen()) {
                    session.getBasicRemote().sendText(message);
                }
            }
        } catch (IOException e) {
            log.error("发送WebSocket消息失败", e);
        }
    }
    
    /**
     * 创建消息JSON字符串
     */
    private static String createMessage(String type, String message, Object data) {
        Map<String, Object> messageMap = new ConcurrentHashMap<>();
        messageMap.put("type", type);
        messageMap.put("message", message);
        messageMap.put("timestamp", System.currentTimeMillis());
        
        if (data != null) {
            messageMap.put("data", data);
        }
        
        return JSONUtil.toJsonStr(messageMap);
    }
    
    /**
     * 获取指定任务的连接数
     */
    public static int getConnectionCount(String taskId) {
        Map<String, Session> sessions = TASK_SESSIONS.get(taskId);
        return sessions != null ? sessions.size() : 0;
    }
    
    /**
     * 获取所有连接数
     */
    public static int getTotalConnectionCount() {
        return TASK_SESSIONS.values().stream()
                .mapToInt(Map::size)
                .sum();
    }
    
    /**
     * 关闭指定任务的所有连接
     */
    public static void closeTaskConnections(String taskId) {
        Map<String, Session> sessions = TASK_SESSIONS.remove(taskId);
        if (sessions != null) {
            sessions.values().forEach(session -> {
                try {
                    if (session.isOpen()) {
                        session.close();
                    }
                } catch (IOException e) {
                    log.error("关闭WebSocket连接失败", e);
                }
            });
        }
    }
}
