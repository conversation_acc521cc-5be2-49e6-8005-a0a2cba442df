package com.cirpoint.datamigrate.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.cirpoint.datamigrate.config.MigrateConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * 配置管理服务
 * 对应批处理脚本中的01-config.bat功能
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
@Service
public class ConfigService {
    
    @Autowired
    private MigrateConfig migrateConfig;
    
    /**
     * 初始化配置
     */
    @PostConstruct
    public void init() {
        // 确保日志目录存在
        ensureLogDirExists();
        log.info("配置管理服务初始化完成");
    }
    
    /**
     * 获取当前配置
     */
    public MigrateConfig getConfig() {
        return migrateConfig;
    }
    
    /**
     * 更新配置
     */
    public void updateConfig(MigrateConfig newConfig) {
        // 验证配置
        validateConfig(newConfig);
        
        // 更新配置
        migrateConfig.setSourceRoot(newConfig.getSourceRoot());
        migrateConfig.setTargetE(newConfig.getTargetE());
        migrateConfig.setTargetF(newConfig.getTargetF());
        migrateConfig.setLogDir(newConfig.getLogDir());
        migrateConfig.setServiceName(newConfig.getServiceName());
        migrateConfig.setStrategy(newConfig.getStrategy());
        migrateConfig.setTargetEMinFreeGb(newConfig.getTargetEMinFreeGb());
        migrateConfig.setTargetFMinFreeGb(newConfig.getTargetFMinFreeGb());
        migrateConfig.setExcludeRecentMonths(newConfig.getExcludeRecentMonths());
        migrateConfig.setThreads(newConfig.getThreads());
        migrateConfig.setRoboRetry(newConfig.getRoboRetry());
        migrateConfig.setRoboWait(newConfig.getRoboWait());
        migrateConfig.setDryRun(newConfig.isDryRun());
        migrateConfig.setDeleteOld(newConfig.isDeleteOld());
        migrateConfig.setVerifySampleCount(newConfig.getVerifySampleCount());
        
        // 确保日志目录存在
        ensureLogDirExists();
        
        log.info("配置已更新");
    }
    
    /**
     * 验证配置有效性
     */
    public void validateConfig(MigrateConfig config) {
        if (StrUtil.isBlank(config.getSourceRoot())) {
            throw new IllegalArgumentException("源目录不能为空");
        }
        
        if (StrUtil.isBlank(config.getTargetE()) && StrUtil.isBlank(config.getTargetF())) {
            throw new IllegalArgumentException("至少需要配置一个目标目录（E盘或F盘）");
        }
        
        if (StrUtil.isBlank(config.getLogDir())) {
            throw new IllegalArgumentException("日志目录不能为空");
        }
        
        if (config.getTargetEMinFreeGb() < 0 || config.getTargetFMinFreeGb() < 0) {
            throw new IllegalArgumentException("预留空间不能为负数");
        }
        
        if (config.getExcludeRecentMonths() < 0) {
            throw new IllegalArgumentException("排除月份数不能为负数");
        }
        
        if (config.getThreads() <= 0 || config.getThreads() > 64) {
            throw new IllegalArgumentException("线程数必须在1-64之间");
        }
        
        if (config.getRoboRetry() < 0 || config.getRoboWait() < 0) {
            throw new IllegalArgumentException("重试次数和等待时间不能为负数");
        }
        
        if (config.getVerifySampleCount() < 0) {
            throw new IllegalArgumentException("抽样验证数量不能为负数");
        }
        
        // 检查目录是否存在
        File sourceDir = new File(config.getSourceRoot());
        if (!sourceDir.exists()) {
            throw new IllegalArgumentException("源目录不存在: " + config.getSourceRoot());
        }
        
        if (!sourceDir.isDirectory()) {
            throw new IllegalArgumentException("源路径不是目录: " + config.getSourceRoot());
        }
    }
    
    /**
     * 检查配置状态
     */
    public Map<String, Object> checkConfigStatus() {
        Map<String, Object> status = new HashMap<>();
        
        // 检查源目录
        File sourceDir = new File(migrateConfig.getSourceRoot());
        status.put("sourceExists", sourceDir.exists() && sourceDir.isDirectory());
        
        // 检查目标目录
        File targetEDir = new File(migrateConfig.getTargetE());
        File targetFDir = new File(migrateConfig.getTargetF());
        status.put("targetEExists", targetEDir.exists() && targetEDir.isDirectory());
        status.put("targetFExists", targetFDir.exists() && targetFDir.isDirectory());
        
        // 检查日志目录
        File logDir = new File(migrateConfig.getLogDir());
        status.put("logDirExists", logDir.exists() && logDir.isDirectory());
        
        // 检查磁盘空间
        if (targetEDir.exists()) {
            long freeSpaceE = targetEDir.getFreeSpace() / (1024 * 1024 * 1024);
            status.put("targetEFreeSpaceGB", freeSpaceE);
            status.put("targetESpaceSufficient", freeSpaceE > migrateConfig.getTargetEMinFreeGb());
        }
        
        if (targetFDir.exists()) {
            long freeSpaceF = targetFDir.getFreeSpace() / (1024 * 1024 * 1024);
            status.put("targetFFreeSpaceGB", freeSpaceF);
            status.put("targetFSpaceSufficient", freeSpaceF > migrateConfig.getTargetFMinFreeGb());
        }
        
        // 检查服务状态（如果配置了服务名）
        if (StrUtil.isNotBlank(migrateConfig.getServiceName())) {
            status.put("serviceConfigured", true);
            // TODO: 检查服务是否存在和运行状态
        } else {
            status.put("serviceConfigured", false);
        }
        
        return status;
    }
    
    /**
     * 获取配置摘要信息
     */
    public Map<String, Object> getConfigSummary() {
        Map<String, Object> summary = new HashMap<>();
        
        summary.put("sourceRoot", migrateConfig.getSourceRoot());
        summary.put("targetE", migrateConfig.getTargetE());
        summary.put("targetF", migrateConfig.getTargetF());
        summary.put("strategy", migrateConfig.getStrategy());
        summary.put("strategyName", MigrateConfig.Strategy.fromCode(migrateConfig.getStrategy()).getDescription());
        summary.put("dryRun", migrateConfig.isDryRun());
        summary.put("deleteOld", migrateConfig.isDeleteOld());
        summary.put("threads", migrateConfig.getThreads());
        summary.put("excludeRecentMonths", migrateConfig.getExcludeRecentMonths());
        summary.put("verifySampleCount", migrateConfig.getVerifySampleCount());
        
        return summary;
    }
    
    /**
     * 确保日志目录存在
     */
    private void ensureLogDirExists() {
        try {
            String logDir = migrateConfig.getLogDir();
            if (StrUtil.isNotBlank(logDir)) {
                FileUtil.mkdir(logDir);
                log.debug("确保日志目录存在: {}", logDir);
            }
        } catch (Exception e) {
            log.error("创建日志目录失败", e);
        }
    }
    
    /**
     * 重置为默认配置
     */
    public void resetToDefault() {
        MigrateConfig defaultConfig = new MigrateConfig();
        updateConfig(defaultConfig);
        log.info("配置已重置为默认值");
    }
}
