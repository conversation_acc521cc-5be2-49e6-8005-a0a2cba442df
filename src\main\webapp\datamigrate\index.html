<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>数据迁移管理系统</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../layui/css/layui.css" media="all">
    <style>
        .layui-layout-admin .layui-header {
            background-color: #393D49;
        }
        .layui-nav-tree .layui-nav-child dd cite {
            color: #666;
        }
        .status-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            color: white;
        }
        .status-not-migrated { background-color: #FF5722; }
        .status-pre-synced { background-color: #FF9800; }
        .status-migrated { background-color: #4CAF50; }
        .status-rollback { background-color: #2196F3; }
        .status-error { background-color: #F44336; }
        
        .task-status-pending { color: #FF9800; }
        .task-status-running { color: #2196F3; }
        .task-status-success { color: #4CAF50; }
        .task-status-failed { color: #F44336; }
        .task-status-cancelled { color: #9E9E9E; }
        
        .progress-container {
            margin: 10px 0;
        }
        
        .log-container {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .config-form .layui-form-item {
            margin-bottom: 15px;
        }
        
        .statistics-card {
            background: white;
            border-radius: 4px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .statistics-number {
            font-size: 24px;
            font-weight: bold;
            color: #1E9FFF;
        }
        
        .statistics-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body class="layui-layout-body">
    <div class="layui-layout layui-layout-admin">
        <!-- 头部区域 -->
        <div class="layui-header">
            <div class="layui-logo" style="color: white; font-weight: bold;">数据迁移管理系统</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item">
                    <a href="javascript:;" id="refreshBtn">
                        <i class="layui-icon layui-icon-refresh-3"></i> 刷新
                    </a>
                </li>
                <li class="layui-nav-item">
                    <a href="javascript:;" id="configBtn">
                        <i class="layui-icon layui-icon-set"></i> 配置
                    </a>
                </li>
            </ul>
        </div>
        
        <!-- 左侧导航区域 -->
        <div class="layui-side layui-bg-black">
            <div class="layui-side-scroll">
                <ul class="layui-nav layui-nav-tree" lay-filter="nav">
                    <li class="layui-nav-item layui-nav-itemed">
                        <a class="" href="javascript:;">
                            <i class="layui-icon layui-icon-home"></i>
                            <cite>概览</cite>
                        </a>
                        <dl class="layui-nav-child">
                            <dd><a href="javascript:;" data-tab="dashboard">系统概览</a></dd>
                            <dd><a href="javascript:;" data-tab="statistics">统计信息</a></dd>
                        </dl>
                    </li>
                    <li class="layui-nav-item">
                        <a href="javascript:;">
                            <i class="layui-icon layui-icon-date"></i>
                            <cite>月份管理</cite>
                        </a>
                        <dl class="layui-nav-child">
                            <dd><a href="javascript:;" data-tab="months">月份列表</a></dd>
                            <dd><a href="javascript:;" data-tab="month-scan">扫描月份</a></dd>
                        </dl>
                    </li>
                    <li class="layui-nav-item">
                        <a href="javascript:;">
                            <i class="layui-icon layui-icon-transfer"></i>
                            <cite>迁移操作</cite>
                        </a>
                        <dl class="layui-nav-child">
                            <dd><a href="javascript:;" data-tab="pre-sync">预同步</a></dd>
                            <dd><a href="javascript:;" data-tab="cutover">正式切换</a></dd>
                            <dd><a href="javascript:;" data-tab="rollback">回滚管理</a></dd>
                            <dd><a href="javascript:;" data-tab="junction">联接管理</a></dd>
                        </dl>
                    </li>
                    <li class="layui-nav-item">
                        <a href="javascript:;">
                            <i class="layui-icon layui-icon-list"></i>
                            <cite>任务管理</cite>
                        </a>
                        <dl class="layui-nav-child">
                            <dd><a href="javascript:;" data-tab="tasks">任务列表</a></dd>
                            <dd><a href="javascript:;" data-tab="running-tasks">运行中任务</a></dd>
                        </dl>
                    </li>
                    <li class="layui-nav-item">
                        <a href="javascript:;">
                            <i class="layui-icon layui-icon-file"></i>
                            <cite>日志管理</cite>
                        </a>
                        <dl class="layui-nav-child">
                            <dd><a href="javascript:;" data-tab="logs">日志查看</a></dd>
                            <dd><a href="javascript:;" data-tab="monitor">实时监控</a></dd>
                        </dl>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- 主体内容区域 -->
        <div class="layui-body">
            <div class="layui-tab layui-tab-brief" lay-filter="mainTab" lay-allowClose="true">
                <ul class="layui-tab-title">
                    <li class="layui-this" lay-id="dashboard">系统概览</li>
                </ul>
                <div class="layui-tab-content">
                    <div class="layui-tab-item layui-show" id="tab-dashboard">
                        <!-- 系统概览内容 -->
                        <div class="layui-row layui-col-space15">
                            <div class="layui-col-md3">
                                <div class="statistics-card">
                                    <div class="statistics-number" id="totalMonths">-</div>
                                    <div class="statistics-label">总月份数</div>
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div class="statistics-card">
                                    <div class="statistics-number" id="migratedMonths">-</div>
                                    <div class="statistics-label">已迁移月份</div>
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div class="statistics-card">
                                    <div class="statistics-number" id="runningTasks">-</div>
                                    <div class="statistics-label">运行中任务</div>
                                </div>
                            </div>
                            <div class="layui-col-md3">
                                <div class="statistics-card">
                                    <div class="statistics-number" id="totalTasks">-</div>
                                    <div class="statistics-label">总任务数</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="layui-row layui-col-space15" style="margin-top: 20px;">
                            <div class="layui-col-md6">
                                <div class="layui-card">
                                    <div class="layui-card-header">最近任务</div>
                                    <div class="layui-card-body">
                                        <table class="layui-table" lay-size="sm">
                                            <thead>
                                                <tr>
                                                    <th>任务名称</th>
                                                    <th>状态</th>
                                                    <th>开始时间</th>
                                                </tr>
                                            </thead>
                                            <tbody id="recentTasksTable">
                                                <tr><td colspan="3" style="text-align: center;">加载中...</td></tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-col-md6">
                                <div class="layui-card">
                                    <div class="layui-card-header">系统状态</div>
                                    <div class="layui-card-body">
                                        <div id="systemStatus">
                                            <p><strong>配置状态：</strong><span id="configStatus">检查中...</span></p>
                                            <p><strong>源目录：</strong><span id="sourceDir">-</span></p>
                                            <p><strong>目标E盘：</strong><span id="targetE">-</span></p>
                                            <p><strong>目标F盘：</strong><span id="targetF">-</span></p>
                                            <p><strong>演练模式：</strong><span id="dryRunMode">-</span></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部固定区域 -->
        <div class="layui-footer">
            © 2025 数据迁移管理系统 - Powered by wanghq
        </div>
    </div>

    <script src="../layui/layui.js"></script>
    <script>
        layui.use(['element', 'layer', 'table', 'form', 'jquery'], function(){
            var element = layui.element;
            var layer = layui.layer;
            var table = layui.table;
            var form = layui.form;
            var $ = layui.$;
            
            // 全局变量
            var currentConfig = {};
            var refreshTimer = null;
            
            // 初始化
            init();
            
            function init() {
                loadDashboard();
                startAutoRefresh();
                bindEvents();
            }
            
            // 绑定事件
            function bindEvents() {
                // 导航点击事件
                $('.layui-nav a[data-tab]').on('click', function() {
                    var tabId = $(this).data('tab');
                    var tabTitle = $(this).text();
                    openTab(tabId, tabTitle);
                });
                
                // 刷新按钮
                $('#refreshBtn').on('click', function() {
                    loadDashboard();
                    layer.msg('刷新完成');
                });
                
                // 配置按钮
                $('#configBtn').on('click', function() {
                    openConfigDialog();
                });
            }
            
            // 加载仪表板数据
            function loadDashboard() {
                // 加载月份统计
                $.get('/api/months/statistics', function(res) {
                    if (res.success) {
                        $('#totalMonths').text(res.data.totalMonths || 0);
                        $('#migratedMonths').text(res.data.migratedCount || 0);
                    }
                });
                
                // 加载任务统计
                $.get('/api/tasks/statistics', function(res) {
                    if (res.success) {
                        $('#runningTasks').text(res.data.runningCount || 0);
                        $('#totalTasks').text(res.data.totalTasks || 0);
                    }
                });
                
                // 加载最近任务
                $.get('/api/tasks/recent?limit=5', function(res) {
                    if (res.success) {
                        var html = '';
                        if (res.data && res.data.length > 0) {
                            res.data.forEach(function(task) {
                                var statusClass = 'task-status-' + task.status.toLowerCase();
                                var startTime = task.startTime ? new Date(task.startTime).toLocaleString() : '-';
                                html += '<tr>';
                                html += '<td>' + task.taskName + '</td>';
                                html += '<td><span class="' + statusClass + '">' + task.status + '</span></td>';
                                html += '<td>' + startTime + '</td>';
                                html += '</tr>';
                            });
                        } else {
                            html = '<tr><td colspan="3" style="text-align: center;">暂无任务</td></tr>';
                        }
                        $('#recentTasksTable').html(html);
                    }
                });
                
                // 加载系统状态
                loadSystemStatus();
            }
            
            // 加载系统状态
            function loadSystemStatus() {
                $.get('/api/config', function(res) {
                    if (res.success) {
                        currentConfig = res.data;
                        $('#sourceDir').text(res.data.sourceRoot || '-');
                        $('#targetE').text(res.data.targetE || '-');
                        $('#targetF').text(res.data.targetF || '-');
                        $('#dryRunMode').text(res.data.dryRun ? '是' : '否');
                    }
                });
                
                $.get('/api/config/status', function(res) {
                    if (res.success) {
                        var status = '正常';
                        if (!res.data.sourceExists) status = '源目录不存在';
                        else if (!res.data.targetEExists && !res.data.targetFExists) status = '目标目录不存在';
                        $('#configStatus').text(status);
                    }
                });
            }
            
            // 打开标签页
            function openTab(tabId, tabTitle) {
                // 检查标签页是否已存在
                var isActive = $('.layui-tab-title li[lay-id="' + tabId + '"]').length > 0;
                
                if (isActive) {
                    // 切换到已存在的标签页
                    element.tabChange('mainTab', tabId);
                } else {
                    // 创建新标签页
                    element.tabAdd('mainTab', {
                        title: tabTitle,
                        content: '<div id="tab-' + tabId + '">加载中...</div>',
                        id: tabId
                    });
                    
                    // 切换到新标签页
                    element.tabChange('mainTab', tabId);
                    
                    // 加载标签页内容
                    loadTabContent(tabId);
                }
            }
            
            // 加载标签页内容
            function loadTabContent(tabId) {
                var container = $('#tab-' + tabId);
                
                switch(tabId) {
                    case 'months':
                        loadMonthsTab(container);
                        break;
                    case 'tasks':
                        loadTasksTab(container);
                        break;
                    case 'pre-sync':
                        loadPreSyncTab(container);
                        break;
                    case 'cutover':
                        loadCutoverTab(container);
                        break;
                    case 'rollback':
                        loadRollbackTab(container);
                        break;
                    case 'junction':
                        loadJunctionTab(container);
                        break;
                    default:
                        container.html('<p>功能开发中...</p>');
                }
            }
            
            // 加载月份管理标签页
            function loadMonthsTab(container) {
                var html = `
                    <div class="layui-card">
                        <div class="layui-card-header">
                            <button class="layui-btn layui-btn-sm" id="scanMonthsBtn">
                                <i class="layui-icon layui-icon-refresh"></i> 扫描月份
                            </button>
                            <button class="layui-btn layui-btn-sm layui-btn-normal" id="refreshMonthsBtn">
                                <i class="layui-icon layui-icon-refresh-3"></i> 刷新
                            </button>
                        </div>
                        <div class="layui-card-body">
                            <table class="layui-hide" id="monthsTable" lay-filter="monthsTable"></table>
                        </div>
                    </div>
                `;
                container.html(html);
                
                // 初始化表格
                table.render({
                    elem: '#monthsTable',
                    url: '/api/months/scan',
                    cols: [[
                        {type: 'checkbox'},
                        {field: 'month', title: '月份', width: 120},
                        {field: 'status', title: '状态', width: 120, templet: function(d) {
                            var statusClass = 'status-' + d.status.toLowerCase().replace('_', '-');
                            return '<span class="status-badge ' + statusClass + '">' + d.status + '</span>';
                        }},
                        {field: 'formattedSourceSize', title: '源目录大小', width: 120},
                        {field: 'formattedTargetSize', title: '目标大小', width: 120},
                        {field: 'targetDrive', title: '目标盘', width: 80},
                        {field: 'isJunction', title: '联接', width: 80, templet: function(d) {
                            return d.isJunction ? '<i class="layui-icon layui-icon-ok" style="color: green;"></i>' : '';
                        }},
                        {field: 'remark', title: '备注'},
                        {title: '操作', width: 200, templet: function(d) {
                            var btns = '';
                            if (d.canPreSync) {
                                btns += '<button class="layui-btn layui-btn-xs" onclick="preSync(\'' + d.month + '\')">预同步</button>';
                            }
                            if (d.canMigrate) {
                                btns += '<button class="layui-btn layui-btn-xs layui-btn-warm" onclick="cutover(\'' + d.month + '\')">切换</button>';
                            }
                            if (d.canRollback) {
                                btns += '<button class="layui-btn layui-btn-xs layui-btn-danger" onclick="rollback(\'' + d.month + '\')">回滚</button>';
                            }
                            return btns || '无操作';
                        }}
                    ]],
                    page: true,
                    limit: 20
                });
                
                // 绑定按钮事件
                $('#scanMonthsBtn').on('click', function() {
                    table.reload('monthsTable');
                    layer.msg('扫描完成');
                });
                
                $('#refreshMonthsBtn').on('click', function() {
                    table.reload('monthsTable');
                });
            }
            
            // 开启自动刷新
            function startAutoRefresh() {
                refreshTimer = setInterval(function() {
                    // 只刷新仪表板的关键数据
                    $.get('/api/tasks/running', function(res) {
                        if (res.success) {
                            $('#runningTasks').text(res.data.length);
                        }
                    });
                }, 30000); // 30秒刷新一次
            }
            
            // 打开配置对话框
            function openConfigDialog() {
                layer.open({
                    type: 2,
                    title: '系统配置',
                    shadeClose: true,
                    shade: 0.8,
                    area: ['800px', '600px'],
                    content: 'config.html'
                });
            }
            
            // 全局函数
            window.preSync = function(month) {
                layer.confirm('确定要对月份 ' + month + ' 执行预同步吗？', function(index) {
                    $.post('/api/migrate/pre-sync', {
                        months: [month]
                    }, function(res) {
                        if (res.success) {
                            layer.msg('预同步任务已启动');
                            // 可以打开任务监控页面
                        } else {
                            layer.msg('启动失败：' + res.message);
                        }
                    });
                    layer.close(index);
                });
            };
            
            window.cutover = function(month) {
                layer.confirm('确定要对月份 ' + month + ' 执行正式切换吗？此操作会停止服务并创建联接！', {
                    btn: ['确定', '取消'],
                    icon: 3,
                    title: '警告'
                }, function(index) {
                    $.post('/api/migrate/cutover', {
                        months: [month]
                    }, function(res) {
                        if (res.success) {
                            layer.msg('正式切换任务已启动');
                        } else {
                            layer.msg('启动失败：' + res.message);
                        }
                    });
                    layer.close(index);
                });
            };
            
            window.rollback = function(month) {
                layer.confirm('确定要对月份 ' + month + ' 执行回滚吗？', function(index) {
                    $.post('/api/migrate/rollback/' + month, {}, function(res) {
                        if (res.success) {
                            layer.msg('回滚任务已启动');
                        } else {
                            layer.msg('启动失败：' + res.message);
                        }
                    });
                    layer.close(index);
                });
            };
        });
    </script>
</body>
</html>
