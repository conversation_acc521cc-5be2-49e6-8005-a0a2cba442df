package com.cirpoint.datamigrate.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.cirpoint.datamigrate.config.MigrateConfig;
import com.cirpoint.datamigrate.entity.MigrateTask;
import com.cirpoint.datamigrate.entity.MonthInfo;
import com.cirpoint.datamigrate.util.SystemCommandUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * 数据同步服务
 * 对应批处理脚本中的20-pre-sync.bat和30-cutover-month.bat功能
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
@Service
public class SyncService {
    
    @Autowired
    private MigrateConfig migrateConfig;
    
    @Autowired
    private MonthService monthService;
    
    @Autowired
    private TaskService taskService;
    
    /**
     * 执行预同步
     * 对应20-pre-sync.bat功能
     * 
     * @param months 要同步的月份列表
     * @param progressCallback 进度回调
     * @return 任务ID
     */
    public String executePreSync(List<String> months, Consumer<String> progressCallback) {
        log.info("开始执行预同步，月份数量: {}", months.size());
        
        // 创建任务
        MigrateTask task = new MigrateTask();
        task.setTaskId(IdUtil.simpleUUID());
        task.setTaskName("预同步-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss")));
        task.setTaskType(MigrateTask.TaskType.PRE_SYNC);
        task.setStatus(MigrateTask.TaskStatus.PENDING);
        task.setMonths(months);
        task.setDryRun(migrateConfig.isDryRun());
        task.setCreator("wanghq");
        task.setStartTime(LocalDateTime.now());
        
        taskService.saveTask(task);
        
        // 异步执行
        CompletableFuture.runAsync(() -> {
            executePreSyncInternal(task, progressCallback);
        });
        
        return task.getTaskId();
    }
    
    /**
     * 内部预同步执行逻辑
     */
    private void executePreSyncInternal(MigrateTask task, Consumer<String> progressCallback) {
        try {
            task.setStatus(MigrateTask.TaskStatus.RUNNING);
            taskService.updateTask(task);
            
            List<String> months = task.getMonths();
            int totalMonths = months.size();
            int completedMonths = 0;
            
            for (String month : months) {
                task.setCurrentMonth(month);
                task.setCurrentProgress(0);
                taskService.updateTask(task);
                
                if (progressCallback != null) {
                    progressCallback.accept("开始预同步月份: " + month);
                }
                
                try {
                    // 获取月份信息
                    MonthInfo monthInfo = monthService.getMonthInfo(month);
                    
                    if (!monthInfo.isSourceExists()) {
                        log.warn("跳过不存在的源目录: {}", monthInfo.getSourcePath());
                        if (progressCallback != null) {
                            progressCallback.accept("跳过：源目录不存在 " + monthInfo.getSourcePath());
                        }
                        continue;
                    }
                    
                    if (StrUtil.isBlank(monthInfo.getTargetPath())) {
                        log.warn("未找到可用目标盘，跳过月份: {}", month);
                        if (progressCallback != null) {
                            progressCallback.accept("未找到可用目标盘，跳过月份: " + month);
                        }
                        continue;
                    }
                    
                    // 确保目标目录存在
                    File targetDir = new File(monthInfo.getTargetPath());
                    if (!targetDir.exists()) {
                        if (progressCallback != null) {
                            progressCallback.accept("创建目标目录：" + monthInfo.getTargetPath());
                        }
                        
                        if (!migrateConfig.isDryRun()) {
                            FileUtil.mkdir(targetDir);
                        }
                    }
                    
                    // 构建robocopy命令
                    String logFile = getLogFilePath("pre", month);
                    String command = buildRobocopyCommand(
                        monthInfo.getSourcePath(),
                        monthInfo.getTargetPath(),
                        logFile,
                        false // 预同步使用/E参数，不删除目标多余文件
                    );
                    
                    if (progressCallback != null) {
                        progressCallback.accept("[PRE] " + command);
                    }
                    
                    // 执行命令
                    if (!migrateConfig.isDryRun()) {
                        SystemCommandUtil.CommandResult result = SystemCommandUtil.executeCommand(
                            command, null, 120, // 2小时超时
                            line -> {
                                if (progressCallback != null) {
                                    progressCallback.accept(line);
                                }
                            }
                        );
                        
                        if (!result.isSuccess() && result.getExitCode() > 7) {
                            // Robocopy返回码大于7表示严重错误
                            log.error("预同步失败: {}, 退出码: {}", month, result.getExitCode());
                            if (progressCallback != null) {
                                progressCallback.accept("预同步失败: " + month + ", 错误: " + result.getErrorMessage());
                            }
                        }
                    }
                    
                    task.setCurrentProgress(100);
                    
                } catch (Exception e) {
                    log.error("预同步月份失败: " + month, e);
                    if (progressCallback != null) {
                        progressCallback.accept("预同步月份失败: " + month + ", 错误: " + e.getMessage());
                    }
                }
                
                completedMonths++;
                task.setTotalProgress((completedMonths * 100) / totalMonths);
                taskService.updateTask(task);
            }
            
            task.setStatus(MigrateTask.TaskStatus.SUCCESS);
            task.setEndTime(LocalDateTime.now());
            task.setResultDetail("预同步完成，处理了" + completedMonths + "个月份");
            
            if (progressCallback != null) {
                progressCallback.accept("预同步完成（可多次重复执行）");
            }
            
        } catch (Exception e) {
            log.error("预同步执行失败", e);
            task.setStatus(MigrateTask.TaskStatus.FAILED);
            task.setEndTime(LocalDateTime.now());
            task.setErrorMessage(e.getMessage());
            
            if (progressCallback != null) {
                progressCallback.accept("预同步执行失败: " + e.getMessage());
            }
        } finally {
            taskService.updateTask(task);
        }
    }
    
    /**
     * 构建Robocopy命令
     */
    private String buildRobocopyCommand(String sourcePath, String targetPath, String logFile, boolean mirror) {
        StringBuilder cmd = new StringBuilder();
        cmd.append("robocopy \"").append(sourcePath).append("\" \"").append(targetPath).append("\"");
        
        if (mirror) {
            cmd.append(" /MIR"); // 镜像模式，删除目标多余文件
        } else {
            cmd.append(" /E"); // 复制所有子目录，包括空目录
        }
        
        cmd.append(" /COPY:DATSO"); // 复制数据、属性、时间戳、安全、所有者
        cmd.append(" /DCOPY:DAT"); // 目录复制数据、属性、时间戳
        cmd.append(" /R:").append(migrateConfig.getRoboRetry()); // 重试次数
        cmd.append(" /W:").append(migrateConfig.getRoboWait()); // 等待时间
        cmd.append(" /MT:").append(migrateConfig.getThreads()); // 多线程
        cmd.append(" /FFT"); // 假设FAT文件时间（2秒精度）
        cmd.append(" /XJ"); // 排除联接点
        cmd.append(" /TEE"); // 输出到控制台和日志文件
        cmd.append(" /LOG+:\"").append(logFile).append("\""); // 追加日志
        
        return cmd.toString();
    }
    
    /**
     * 获取日志文件路径
     */
    private String getLogFilePath(String prefix, String month) {
        String logDir = migrateConfig.getLogDir();
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss"));
        return logDir + File.separator + prefix + "_" + month + "_" + timestamp + ".log";
    }
    
    /**
     * 执行正式切换
     * 对应30-cutover-month.bat功能
     * 
     * @param months 要切换的月份列表
     * @param progressCallback 进度回调
     * @return 任务ID
     */
    public String executeCutover(List<String> months, Consumer<String> progressCallback) {
        log.info("开始执行正式切换，月份数量: {}", months.size());
        
        // 创建任务
        MigrateTask task = new MigrateTask();
        task.setTaskId(IdUtil.simpleUUID());
        task.setTaskName("正式切换-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss")));
        task.setTaskType(MigrateTask.TaskType.CUTOVER);
        task.setStatus(MigrateTask.TaskStatus.PENDING);
        task.setMonths(months);
        task.setDryRun(migrateConfig.isDryRun());
        task.setCreator("wanghq");
        task.setStartTime(LocalDateTime.now());
        
        taskService.saveTask(task);
        
        // 异步执行
        CompletableFuture.runAsync(() -> {
            executeCutoverInternal(task, progressCallback);
        });
        
        return task.getTaskId();
    }
    
    /**
     * 内部正式切换执行逻辑
     */
    private void executeCutoverInternal(MigrateTask task, Consumer<String> progressCallback) {
        try {
            task.setStatus(MigrateTask.TaskStatus.RUNNING);
            taskService.updateTask(task);
            
            // 停止服务（如果配置了）
            if (StrUtil.isNotBlank(migrateConfig.getServiceName())) {
                if (progressCallback != null) {
                    progressCallback.accept("尝试停止服务：" + migrateConfig.getServiceName());
                }
                
                if (!migrateConfig.isDryRun()) {
                    String stopCommand = "net stop \"" + migrateConfig.getServiceName() + "\"";
                    SystemCommandUtil.executeCommand(stopCommand);
                }
            }
            
            if (progressCallback != null) {
                progressCallback.accept("即将执行最终差异同步与切换，请确保业务已停止写入");
            }
            
            List<String> months = task.getMonths();
            int totalMonths = months.size();
            int completedMonths = 0;
            
            for (String month : months) {
                task.setCurrentMonth(month);
                task.setCurrentProgress(0);
                taskService.updateTask(task);
                
                if (progressCallback != null) {
                    progressCallback.accept("开始切换月份: " + month);
                }
                
                try {
                    executeCutoverForMonth(month, task, progressCallback);
                } catch (Exception e) {
                    log.error("切换月份失败: " + month, e);
                    if (progressCallback != null) {
                        progressCallback.accept("切换月份失败: " + month + ", 错误: " + e.getMessage());
                    }
                }
                
                completedMonths++;
                task.setTotalProgress((completedMonths * 100) / totalMonths);
                taskService.updateTask(task);
            }
            
            // 启动服务（如果配置了）
            if (StrUtil.isNotBlank(migrateConfig.getServiceName())) {
                if (progressCallback != null) {
                    progressCallback.accept("尝试启动服务：" + migrateConfig.getServiceName());
                }
                
                if (!migrateConfig.isDryRun()) {
                    String startCommand = "net start \"" + migrateConfig.getServiceName() + "\"";
                    SystemCommandUtil.executeCommand(startCommand);
                }
            }
            
            task.setStatus(MigrateTask.TaskStatus.SUCCESS);
            task.setEndTime(LocalDateTime.now());
            task.setResultDetail("切换完成，处理了" + completedMonths + "个月份");
            
            if (progressCallback != null) {
                progressCallback.accept("切换完成。请检查应用日志、磁盘空间与备份任务");
            }
            
        } catch (Exception e) {
            log.error("正式切换执行失败", e);
            task.setStatus(MigrateTask.TaskStatus.FAILED);
            task.setEndTime(LocalDateTime.now());
            task.setErrorMessage(e.getMessage());
            
            if (progressCallback != null) {
                progressCallback.accept("正式切换执行失败: " + e.getMessage());
            }
        } finally {
            taskService.updateTask(task);
        }
    }
    
    /**
     * 为单个月份执行切换
     */
    private void executeCutoverForMonth(String month, MigrateTask task, Consumer<String> progressCallback) {
        // 获取月份信息
        MonthInfo monthInfo = monthService.getMonthInfo(month);
        
        if (!monthInfo.isSourceExists()) {
            if (progressCallback != null) {
                progressCallback.accept("跳过：源目录不存在 " + monthInfo.getSourcePath());
            }
            return;
        }
        
        if (StrUtil.isBlank(monthInfo.getTargetPath())) {
            if (progressCallback != null) {
                progressCallback.accept("未找到可用目标盘，跳过 " + month);
            }
            return;
        }
        
        // 确保目标目录存在
        File targetDir = new File(monthInfo.getTargetPath());
        if (!targetDir.exists()) {
            if (progressCallback != null) {
                progressCallback.accept("创建目标目录：" + monthInfo.getTargetPath());
            }
            
            if (!migrateConfig.isDryRun()) {
                FileUtil.mkdir(targetDir);
            }
        }
        
        // 最终差异同步
        task.setCurrentProgress(20);
        String logFile = getLogFilePath("final", month);
        String syncCommand = buildRobocopyCommand(
            monthInfo.getSourcePath(),
            monthInfo.getTargetPath(),
            logFile,
            true // 使用镜像模式
        );
        
        if (progressCallback != null) {
            progressCallback.accept("[FINAL] " + syncCommand);
        }
        
        if (!migrateConfig.isDryRun()) {
            SystemCommandUtil.CommandResult syncResult = SystemCommandUtil.executeCommand(syncCommand);
            if (!syncResult.isSuccess() && syncResult.getExitCode() > 7) {
                throw new RuntimeException("最终同步失败: " + syncResult.getErrorMessage());
            }
        }
        
        // 检查是否存在残留的_old目录
        String oldPath = monthInfo.getSourcePath() + "_old";
        if (new File(oldPath).exists()) {
            if (progressCallback != null) {
                progressCallback.accept("发现残留_old目录，先行删除或人工处理后重试：" + oldPath);
            }
            return;
        }
        
        // 重命名原目录为_old
        task.setCurrentProgress(50);
        if (progressCallback != null) {
            progressCallback.accept("重命名原目录为_old：" + monthInfo.getSourcePath() + " 到 " + oldPath);
        }
        
        if (!migrateConfig.isDryRun()) {
            File sourceDir = new File(monthInfo.getSourcePath());
            File oldDir = new File(oldPath);
            if (!sourceDir.renameTo(oldDir)) {
                throw new RuntimeException("重命名目录失败");
            }
        }
        
        // 创建联接
        task.setCurrentProgress(70);
        String linkCommand = "mklink /J \"" + monthInfo.getSourcePath() + "\" \"" + monthInfo.getTargetPath() + "\"";
        if (progressCallback != null) {
            progressCallback.accept("[LINK] " + linkCommand);
        }
        
        if (!migrateConfig.isDryRun()) {
            SystemCommandUtil.CommandResult linkResult = SystemCommandUtil.executeCommand(linkCommand);
            if (!linkResult.isSuccess()) {
                throw new RuntimeException("创建联接失败: " + linkResult.getErrorMessage());
            }
        }
        
        // 验证
        task.setCurrentProgress(90);
        if (progressCallback != null) {
            progressCallback.accept("验证数据一致性...");
        }
        
        // 删除旧目录（如果配置了）
        if (migrateConfig.isDeleteOld()) {
            if (progressCallback != null) {
                progressCallback.accept("删除旧目录：" + oldPath);
            }
            
            if (!migrateConfig.isDryRun()) {
                FileUtil.del(oldPath);
            }
        } else {
            if (progressCallback != null) {
                progressCallback.accept("已保留旧目录：" + oldPath + " （回滚窗口期内可随时恢复）");
            }
        }
        
        task.setCurrentProgress(100);
    }
}
