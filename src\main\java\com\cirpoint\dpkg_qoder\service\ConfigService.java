package com.cirpoint.dpkg_qoder.service;

import com.cirpoint.dpkg_qoder.model.MigrateConfig;
import com.cirpoint.dpkg_qoder.model.MigrateConstants;
import com.cirpoint.dpkg_qoder.model.OperationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Properties;

/**
 * 配置管理服务
 * 负责加载、保存和管理迁移配置
 * 
 * <AUTHOR> IDE
 * @since 1.0.0
 */
@Slf4j
@Service
public class ConfigService {
    
    /**
     * 当前配置对象
     */
    private MigrateConfig currentConfig;
    
    /**
     * 配置文件路径
     */
    private String configFilePath;
    
    @PostConstruct
    public void init() {
        // 初始化配置文件路径
        this.configFilePath = System.getProperty("user.dir") + File.separator + MigrateConstants.DEFAULT_CONFIG_PATH;
        
        // 创建配置目录
        Path configDir = Paths.get(configFilePath).getParent();
        try {
            if (!Files.exists(configDir)) {
                Files.createDirectories(configDir);
                log.info("创建配置目录: {}", configDir);
            }
        } catch (IOException e) {
            log.error("创建配置目录失败: {}", configDir, e);
        }
        
        // 加载配置
        loadConfig();
    }
    
    /**
     * 获取当前配置
     */
    public MigrateConfig getConfig() {
        if (currentConfig == null) {
            currentConfig = createDefaultConfig();
        }
        return currentConfig;
    }
    
    /**
     * 更新配置
     */
    public OperationResult<MigrateConfig> updateConfig(MigrateConfig config) {
        try {
            // 验证配置
            OperationResult<Void> validationResult = validateConfig(config);
            if (!validationResult.isSuccess()) {
                return OperationResult.failure("配置验证失败: " + validationResult.getMessage());
            }
            
            // 保存配置
            saveConfig(config);
            
            // 更新当前配置
            this.currentConfig = config;
            
            log.info("配置更新成功");
            return OperationResult.success(config, "配置更新成功");
            
        } catch (Exception e) {
            log.error("更新配置失败", e);
            return OperationResult.failure("更新配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 重置为默认配置
     */
    public OperationResult<MigrateConfig> resetToDefault() {
        try {
            MigrateConfig defaultConfig = createDefaultConfig();
            saveConfig(defaultConfig);
            this.currentConfig = defaultConfig;
            
            log.info("配置已重置为默认值");
            return OperationResult.success(defaultConfig, "配置已重置为默认值");
            
        } catch (Exception e) {
            log.error("重置配置失败", e);
            return OperationResult.failure("重置配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 加载配置
     */
    private void loadConfig() {
        try {
            File configFile = new File(configFilePath);
            if (!configFile.exists()) {
                log.info("配置文件不存在，创建默认配置: {}", configFilePath);
                currentConfig = createDefaultConfig();
                saveConfig(currentConfig);
                return;
            }
            
            Properties props = new Properties();
            try (InputStreamReader reader = new InputStreamReader(
                    new FileInputStream(configFile), StandardCharsets.UTF_8)) {
                props.load(reader);
            }
            
            currentConfig = createConfigFromProperties(props);
            log.info("配置加载成功: {}", configFilePath);
            
        } catch (Exception e) {
            log.error("加载配置失败，使用默认配置", e);
            currentConfig = createDefaultConfig();
        }
    }
    
    /**
     * 保存配置
     */
    private void saveConfig(MigrateConfig config) throws IOException {
        Properties props = createPropertiesFromConfig(config);
        
        File configFile = new File(configFilePath);
        try (OutputStreamWriter writer = new OutputStreamWriter(
                new FileOutputStream(configFile), StandardCharsets.UTF_8)) {
            props.store(writer, "Data Migration Configuration - Generated by Qoder IDE");
        }
        
        log.info("配置保存成功: {}", configFilePath);
    }
    
    /**
     * 创建默认配置
     */
    private MigrateConfig createDefaultConfig() {
        return new MigrateConfig();
    }
    
    /**
     * 从Properties创建配置对象
     */
    private MigrateConfig createConfigFromProperties(Properties props) {
        MigrateConfig config = new MigrateConfig();
        
        config.setSourceRoot(props.getProperty("source.root", config.getSourceRoot()));
        config.setTargetE(props.getProperty("target.e", config.getTargetE()));
        config.setTargetF(props.getProperty("target.f", config.getTargetF()));
        config.setLogDir(props.getProperty("log.dir", config.getLogDir()));
        config.setServiceName(props.getProperty("service.name", config.getServiceName()));
        
        String strategyStr = props.getProperty("allocation.strategy", config.getStrategy().name());
        try {
            config.setStrategy(MigrateConfig.AllocationStrategy.valueOf(strategyStr));
        } catch (IllegalArgumentException e) {
            log.warn("无效的分配策略: {}, 使用默认策略", strategyStr);
        }
        
        config.setTargetEMinFreeGb(Integer.parseInt(
                props.getProperty("target.e.min.free.gb", config.getTargetEMinFreeGb().toString())));
        config.setTargetFMinFreeGb(Integer.parseInt(
                props.getProperty("target.f.min.free.gb", config.getTargetFMinFreeGb().toString())));
        config.setExcludeRecentMonths(Integer.parseInt(
                props.getProperty("exclude.recent.months", config.getExcludeRecentMonths().toString())));
        config.setThreads(Integer.parseInt(
                props.getProperty("robocopy.threads", config.getThreads().toString())));
        config.setRoboRetry(Integer.parseInt(
                props.getProperty("robocopy.retry", config.getRoboRetry().toString())));
        config.setRoboWait(Integer.parseInt(
                props.getProperty("robocopy.wait", config.getRoboWait().toString())));
        config.setDryRun(Boolean.parseBoolean(
                props.getProperty("dry.run", config.getDryRun().toString())));
        config.setDeleteOld(Boolean.parseBoolean(
                props.getProperty("delete.old", config.getDeleteOld().toString())));
        config.setVerifySampleCount(Integer.parseInt(
                props.getProperty("verify.sample.count", config.getVerifySampleCount().toString())));
        
        return config;
    }
    
    /**
     * 从配置对象创建Properties
     */
    private Properties createPropertiesFromConfig(MigrateConfig config) {
        Properties props = new Properties();
        
        props.setProperty("source.root", config.getSourceRoot());
        props.setProperty("target.e", config.getTargetE());
        props.setProperty("target.f", config.getTargetF());
        props.setProperty("log.dir", config.getLogDir());
        props.setProperty("service.name", config.getServiceName());
        props.setProperty("allocation.strategy", config.getStrategy().name());
        props.setProperty("target.e.min.free.gb", config.getTargetEMinFreeGb().toString());
        props.setProperty("target.f.min.free.gb", config.getTargetFMinFreeGb().toString());
        props.setProperty("exclude.recent.months", config.getExcludeRecentMonths().toString());
        props.setProperty("robocopy.threads", config.getThreads().toString());
        props.setProperty("robocopy.retry", config.getRoboRetry().toString());
        props.setProperty("robocopy.wait", config.getRoboWait().toString());
        props.setProperty("dry.run", config.getDryRun().toString());
        props.setProperty("delete.old", config.getDeleteOld().toString());
        props.setProperty("verify.sample.count", config.getVerifySampleCount().toString());
        
        return props;
    }
    
    /**
     * 验证配置
     */
    private OperationResult<Void> validateConfig(MigrateConfig config) {
        if (config == null) {
            return OperationResult.failure("配置对象不能为空");
        }
        
        // 验证路径
        if (isNullOrEmpty(config.getSourceRoot())) {
            return OperationResult.failure("源目录不能为空");
        }
        if (isNullOrEmpty(config.getTargetE())) {
            return OperationResult.failure("E盘目标目录不能为空");
        }
        if (isNullOrEmpty(config.getTargetF())) {
            return OperationResult.failure("F盘目标目录不能为空");
        }
        if (isNullOrEmpty(config.getLogDir())) {
            return OperationResult.failure("日志目录不能为空");
        }
        
        // 验证数值范围
        if (config.getTargetEMinFreeGb() < 0) {
            return OperationResult.failure("E盘最小预留空间必须大于等于0");
        }
        if (config.getTargetFMinFreeGb() < 0) {
            return OperationResult.failure("F盘最小预留空间必须大于等于0");
        }
        if (config.getExcludeRecentMonths() < 0) {
            return OperationResult.failure("排除最近月份数量必须大于等于0");
        }
        if (config.getThreads() <= 0 || config.getThreads() > 128) {
            return OperationResult.failure("线程数必须在1-128之间");
        }
        if (config.getRoboRetry() < 0) {
            return OperationResult.failure("重试次数必须大于等于0");
        }
        if (config.getRoboWait() < 0) {
            return OperationResult.failure("重试等待时间必须大于等于0");
        }
        if (config.getVerifySampleCount() < 0) {
            return OperationResult.failure("抽样验证数量必须大于等于0");
        }
        
        return OperationResult.success(null);
    }
    
    /**
     * 检查字符串是否为空
     */
    private boolean isNullOrEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
}