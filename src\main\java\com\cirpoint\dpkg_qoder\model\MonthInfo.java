package com.cirpoint.dpkg_qoder.model;

import lombok.Data;

/**
 * 月份信息模型
 * 
 * <AUTHOR> IDE
 * @since 1.0.0
 */
@Data
public class MonthInfo {
    
    /**
     * 月份名称（格式：yyyy-MM）
     */
    private String monthName;
    
    /**
     * 源目录路径
     */
    private String sourcePath;
    
    /**
     * 目标目录路径
     */
    private String targetPath;
    
    /**
     * 目录大小（字节）
     */
    private Long directorySize;
    
    /**
     * 格式化的目录大小
     */
    private String formattedSize;
    
    /**
     * 是否存在
     */
    private Boolean exists;
    
    /**
     * 是否为联接点
     */
    private Boolean isJunction;
    
    /**
     * 联接目标路径（如果是联接点）
     */
    private String junctionTarget;
    
    /**
     * 状态
     */
    private MonthStatus status;
    
    /**
     * 是否可以迁移
     */
    private Boolean canMigrate;
    
    /**
     * 不能迁移的原因
     */
    private String cannotMigrateReason;
    
    /**
     * 推荐的目标驱动器
     */
    private String recommendedTarget;
    
    /**
     * 月份状态枚举
     */
    public enum MonthStatus {
        /**
         * 普通目录（可迁移）
         */
        NORMAL,
        
        /**
         * 已迁移（联接点）
         */
        MIGRATED,
        
        /**
         * 不存在
         */
        NOT_EXISTS,
        
        /**
         * 错误状态
         */
        ERROR
    }
    
    public MonthInfo() {}
    
    public MonthInfo(String monthName) {
        this.monthName = monthName;
    }
}