package com.cirpoint.dpkg_qoder.util;

import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * 系统命令执行工具类
 * 提供执行Windows系统命令的功能
 * 
 * <AUTHOR> IDE
 * @since 1.0.0
 */
@Slf4j
public class CommandExecutor {
    
    /**
     * 默认超时时间（分钟）
     */
    private static final int DEFAULT_TIMEOUT_MINUTES = 60;
    
    /**
     * Windows系统编码
     */
    private static final Charset WINDOWS_CHARSET = Charset.forName("GBK");
    
    /**
     * 命令执行结果
     */
    public static class CommandResult {
        private final int exitCode;
        private final List<String> outputLines;
        private final List<String> errorLines;
        private final boolean success;
        
        public CommandResult(int exitCode, List<String> outputLines, List<String> errorLines) {
            this.exitCode = exitCode;
            this.outputLines = outputLines != null ? outputLines : new ArrayList<>();
            this.errorLines = errorLines != null ? errorLines : new ArrayList<>();
            this.success = exitCode == 0;
        }
        
        public int getExitCode() { return exitCode; }
        public List<String> getOutputLines() { return outputLines; }
        public List<String> getErrorLines() { return errorLines; }
        public boolean isSuccess() { return success; }
        
        public String getOutput() {
            return String.join("\n", outputLines);
        }
        
        public String getError() {
            return String.join("\n", errorLines);
        }
    }
    
    /**
     * 执行命令（同步）
     */
    public static CommandResult execute(String command) {
        return execute(command, null, DEFAULT_TIMEOUT_MINUTES);
    }
    
    /**
     * 执行命令（同步，指定工作目录）
     */
    public static CommandResult execute(String command, File workingDirectory) {
        return execute(command, workingDirectory, DEFAULT_TIMEOUT_MINUTES);
    }
    
    /**
     * 执行命令（同步，指定超时）
     */
    public static CommandResult execute(String command, File workingDirectory, int timeoutMinutes) {
        log.info("执行命令: {}", command);
        
        try {
            ProcessBuilder processBuilder = new ProcessBuilder("cmd", "/c", command);
            
            if (workingDirectory != null) {
                processBuilder.directory(workingDirectory);
                log.debug("工作目录: {}", workingDirectory.getAbsolutePath());
            }
            
            // 合并错误流到标准输出
            processBuilder.redirectErrorStream(false);
            
            Process process = processBuilder.start();
            
            List<String> outputLines = new ArrayList<>();
            List<String> errorLines = new ArrayList<>();
            
            // 读取标准输出
            Thread outputReader = new Thread(() -> {
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(process.getInputStream(), WINDOWS_CHARSET))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        outputLines.add(line);
                        log.debug("STDOUT: {}", line);
                    }
                } catch (IOException e) {
                    log.error("读取命令输出失败", e);
                }
            });
            
            // 读取错误输出
            Thread errorReader = new Thread(() -> {
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(process.getErrorStream(), WINDOWS_CHARSET))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        errorLines.add(line);
                        log.warn("STDERR: {}", line);
                    }
                } catch (IOException e) {
                    log.error("读取命令错误输出失败", e);
                }
            });
            
            outputReader.start();
            errorReader.start();
            
            // 等待进程完成
            boolean finished = process.waitFor(timeoutMinutes, TimeUnit.MINUTES);
            
            if (!finished) {
                log.error("命令执行超时，强制终止进程");
                process.destroyForcibly();
                return new CommandResult(-1, outputLines, 
                        List.of("命令执行超时(" + timeoutMinutes + "分钟)"));
            }
            
            // 等待输出读取完成
            outputReader.join(5000);
            errorReader.join(5000);
            
            int exitCode = process.exitValue();
            CommandResult result = new CommandResult(exitCode, outputLines, errorLines);
            
            log.info("命令执行完成，退出代码: {}", exitCode);
            return result;
            
        } catch (Exception e) {
            log.error("执行命令失败: {}", command, e);
            return new CommandResult(-1, null, List.of("执行失败: " + e.getMessage()));
        }
    }
    
    /**
     * 执行命令（异步，带输出回调）
     */
    public static Process executeAsync(String command, 
                                     File workingDirectory,
                                     Consumer<String> outputCallback,
                                     Consumer<String> errorCallback) {
        log.info("异步执行命令: {}", command);
        
        try {
            ProcessBuilder processBuilder = new ProcessBuilder("cmd", "/c", command);
            
            if (workingDirectory != null) {
                processBuilder.directory(workingDirectory);
                log.debug("工作目录: {}", workingDirectory.getAbsolutePath());
            }
            
            Process process = processBuilder.start();
            
            // 异步读取标准输出
            if (outputCallback != null) {
                Thread outputReader = new Thread(() -> {
                    try (BufferedReader reader = new BufferedReader(
                            new InputStreamReader(process.getInputStream(), WINDOWS_CHARSET))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            outputCallback.accept(line);
                        }
                    } catch (IOException e) {
                        log.error("读取命令输出失败", e);
                    }
                });
                outputReader.setDaemon(true);
                outputReader.start();
            }
            
            // 异步读取错误输出
            if (errorCallback != null) {
                Thread errorReader = new Thread(() -> {
                    try (BufferedReader reader = new BufferedReader(
                            new InputStreamReader(process.getErrorStream(), WINDOWS_CHARSET))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            errorCallback.accept(line);
                        }
                    } catch (IOException e) {
                        log.error("读取命令错误输出失败", e);
                    }
                });
                errorReader.setDaemon(true);
                errorReader.start();
            }
            
            return process;
            
        } catch (Exception e) {
            log.error("异步执行命令失败: {}", command, e);
            throw new RuntimeException("异步执行命令失败", e);
        }
    }
    
    /**
     * 检查命令是否可用
     */
    public static boolean isCommandAvailable(String command) {
        try {
            CommandResult result = execute("where " + command);
            return result.isSuccess();
        } catch (Exception e) {
            log.debug("检查命令可用性失败: {}", command, e);
            return false;
        }
    }
    
    /**
     * 获取系统信息
     */
    public static String getSystemInfo() {
        CommandResult result = execute("systeminfo");
        return result.isSuccess() ? result.getOutput() : "获取系统信息失败";
    }
    
    /**
     * 检查是否以管理员权限运行
     */
    public static boolean isRunningAsAdmin() {
        try {
            CommandResult result = execute("net session");
            return result.isSuccess();
        } catch (Exception e) {
            log.debug("检查管理员权限失败", e);
            return false;
        }
    }
}