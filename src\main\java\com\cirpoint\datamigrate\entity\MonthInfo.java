package com.cirpoint.datamigrate.entity;

import lombok.Data;

import java.io.File;
import java.time.LocalDateTime;

/**
 * 月份信息实体类
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Data
public class MonthInfo {
    
    /**
     * 月份字符串，格式：yyyy-MM
     */
    private String month;
    
    /**
     * 源目录路径
     */
    private String sourcePath;
    
    /**
     * 目标目录路径
     */
    private String targetPath;
    
    /**
     * 目标磁盘（E或F）
     */
    private String targetDrive;
    
    /**
     * 源目录大小（字节）
     */
    private long sourceSize;
    
    /**
     * 目标目录大小（字节）
     */
    private long targetSize;
    
    /**
     * 是否存在源目录
     */
    private boolean sourceExists;
    
    /**
     * 是否存在目标目录
     */
    private boolean targetExists;
    
    /**
     * 是否为联接目录
     */
    private boolean isJunction;
    
    /**
     * 是否存在_old备份目录
     */
    private boolean hasOldBackup;
    
    /**
     * 迁移状态
     */
    private MigrateStatus status;
    
    /**
     * 最后修改时间
     */
    private LocalDateTime lastModified;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 是否选中（用于批量操作）
     */
    private boolean selected;
    
    /**
     * 备注信息
     */
    private String remark;
    
    /**
     * 迁移状态枚举
     */
    public enum MigrateStatus {
        NOT_MIGRATED("未迁移", "源目录存在，未进行迁移"),
        PRE_SYNCED("预同步完成", "已完成预同步，等待正式切换"),
        MIGRATED("已迁移", "已完成迁移，创建了联接"),
        ROLLBACK("已回滚", "已回滚到原始状态"),
        ERROR("错误", "迁移过程中出现错误");
        
        private final String name;
        private final String description;
        
        MigrateStatus(String name, String description) {
            this.name = name;
            this.description = description;
        }
        
        public String getName() {
            return name;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 获取格式化的源目录大小
     */
    public String getFormattedSourceSize() {
        return formatSize(sourceSize);
    }
    
    /**
     * 获取格式化的目标目录大小
     */
    public String getFormattedTargetSize() {
        return formatSize(targetSize);
    }
    
    /**
     * 格式化文件大小
     */
    private String formatSize(long size) {
        if (size <= 0) return "0 B";
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        
        return String.format("%.2f %s", 
            size / Math.pow(1024, digitGroups), 
            units[digitGroups]);
    }
    
    /**
     * 检查是否可以进行预同步
     */
    public boolean canPreSync() {
        return sourceExists && (status == MigrateStatus.NOT_MIGRATED || status == MigrateStatus.ERROR);
    }
    
    /**
     * 检查是否可以进行正式迁移
     */
    public boolean canMigrate() {
        return sourceExists && !isJunction && 
               (status == MigrateStatus.PRE_SYNCED || status == MigrateStatus.NOT_MIGRATED);
    }
    
    /**
     * 检查是否可以回滚
     */
    public boolean canRollback() {
        return isJunction || hasOldBackup;
    }
}
