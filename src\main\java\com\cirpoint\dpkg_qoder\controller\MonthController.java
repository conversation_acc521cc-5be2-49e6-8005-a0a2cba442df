package com.cirpoint.dpkg_qoder.controller;

import com.cirpoint.dpkg_qoder.model.DiskSpaceInfo;
import com.cirpoint.dpkg_qoder.model.MonthInfo;
import com.cirpoint.dpkg_qoder.model.OperationResult;
import com.cirpoint.dpkg_qoder.service.MonthListService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 月份管理控制器
 * 提供月份管理相关的REST API
 * 
 * <AUTHOR> IDE
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/dpkg/months")
@CrossOrigin(origins = "*")
public class MonthController {
    
    @Autowired
    private MonthListService monthListService;
    
    /**
     * 获取所有月份目录
     */
    @GetMapping
    public OperationResult<List<MonthInfo>> getAllMonths() {
        try {
            log.info("获取所有月份目录");
            return monthListService.getAllMonths();
        } catch (Exception e) {
            log.error("获取所有月份目录失败", e);
            return OperationResult.failure("获取月份列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取可迁移的月份（排除最近N个月）
     */
    @GetMapping("/migratable")
    public OperationResult<List<MonthInfo>> getMigratableMonths() {
        try {
            log.info("获取可迁移月份列表");
            return monthListService.getMigratableMonths();
        } catch (Exception e) {
            log.error("获取可迁移月份列表失败", e);
            return OperationResult.failure("获取可迁移月份失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取指定月份信息
     */
    @GetMapping("/{monthName}")
    public OperationResult<MonthInfo> getMonthInfo(@PathVariable String monthName) {
        try {
            log.info("获取月份信息: {}", monthName);
            return monthListService.getMonthInfo(monthName);
        } catch (Exception e) {
            log.error("获取月份信息失败: {}", monthName, e);
            return OperationResult.failure("获取月份信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取磁盘空间信息
     */
    @GetMapping("/disk-space")
    public OperationResult<List<DiskSpaceInfo>> getDiskSpaceInfo() {
        try {
            log.info("获取磁盘空间信息");
            return monthListService.getDiskSpaceInfo();
        } catch (Exception e) {
            log.error("获取磁盘空间信息失败", e);
            return OperationResult.failure("获取磁盘空间信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 为月份选择推荐的目标驱动器
     */
    @GetMapping("/{monthName}/target-drive")
    public OperationResult<String> selectTargetDrive(@PathVariable String monthName) {
        try {
            log.info("为月份选择目标驱动器: {}", monthName);
            return monthListService.selectTargetDrive(monthName);
        } catch (Exception e) {
            log.error("选择目标驱动器失败: {}", monthName, e);
            return OperationResult.failure("选择目标驱动器失败: " + e.getMessage());
        }
    }
    
    /**
     * 刷新月份列表
     */
    @PostMapping("/refresh")
    public OperationResult<List<MonthInfo>> refreshMonths() {
        try {
            log.info("刷新月份列表");
            // 直接调用获取所有月份，因为每次都会重新扫描
            return monthListService.getAllMonths();
        } catch (Exception e) {
            log.error("刷新月份列表失败", e);
            return OperationResult.failure("刷新月份列表失败: " + e.getMessage());
        }
    }
}