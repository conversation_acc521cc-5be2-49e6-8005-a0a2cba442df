package com.cirpoint.datamigrate.entity;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 迁移任务实体类
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Data
public class MigrateTask {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 任务类型
     */
    private TaskType taskType;
    
    /**
     * 任务状态
     */
    private TaskStatus status;
    
    /**
     * 要处理的月份列表
     */
    private List<String> months;
    
    /**
     * 当前处理的月份
     */
    private String currentMonth;
    
    /**
     * 总进度（0-100）
     */
    private int totalProgress;
    
    /**
     * 当前月份进度（0-100）
     */
    private int currentProgress;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 日志文件路径
     */
    private String logFilePath;
    
    /**
     * 执行结果详情
     */
    private String resultDetail;
    
    /**
     * 是否为演练模式
     */
    private boolean dryRun;
    
    /**
     * 创建者
     */
    private String creator;
    
    /**
     * 任务类型枚举
     */
    public enum TaskType {
        LIST_MONTHS("生成月份清单", "扫描源目录生成月份清单"),
        PRE_SYNC("预同步", "执行预同步操作"),
        CUTOVER("正式切换", "执行正式迁移切换"),
        ROLLBACK("回滚", "回滚迁移操作"),
        CREATE_LINK("创建联接", "创建目录联接"),
        RUN_ALL("一键执行", "执行完整迁移流程");
        
        private final String name;
        private final String description;
        
        TaskType(String name, String description) {
            this.name = name;
            this.description = description;
        }
        
        public String getName() {
            return name;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        PENDING("等待中", "任务已创建，等待执行"),
        RUNNING("执行中", "任务正在执行"),
        SUCCESS("成功", "任务执行成功"),
        FAILED("失败", "任务执行失败"),
        CANCELLED("已取消", "任务被用户取消");
        
        private final String name;
        private final String description;
        
        TaskStatus(String name, String description) {
            this.name = name;
            this.description = description;
        }
        
        public String getName() {
            return name;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 获取执行耗时（毫秒）
     */
    public long getExecutionTime() {
        if (startTime == null) return 0;
        LocalDateTime end = endTime != null ? endTime : LocalDateTime.now();
        return java.time.Duration.between(startTime, end).toMillis();
    }
    
    /**
     * 获取格式化的执行耗时
     */
    public String getFormattedExecutionTime() {
        long millis = getExecutionTime();
        long seconds = millis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        
        if (hours > 0) {
            return String.format("%d小时%d分钟%d秒", hours, minutes % 60, seconds % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟%d秒", minutes, seconds % 60);
        } else {
            return String.format("%d秒", seconds);
        }
    }
    
    /**
     * 检查任务是否正在运行
     */
    public boolean isRunning() {
        return status == TaskStatus.RUNNING;
    }
    
    /**
     * 检查任务是否已完成
     */
    public boolean isCompleted() {
        return status == TaskStatus.SUCCESS || status == TaskStatus.FAILED || status == TaskStatus.CANCELLED;
    }
}
