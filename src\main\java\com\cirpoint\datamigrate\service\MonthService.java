package com.cirpoint.datamigrate.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.cirpoint.datamigrate.config.MigrateConfig;
import com.cirpoint.datamigrate.entity.MonthInfo;
import com.cirpoint.datamigrate.util.SystemCommandUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 月份管理服务
 * 对应批处理脚本中的10-list-months.bat功能
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
@Service
public class MonthService {
    
    @Autowired
    private MigrateConfig migrateConfig;
    
    /**
     * 月份格式正则表达式
     */
    private static final Pattern MONTH_PATTERN = Pattern.compile("^\\d{4}-\\d{2}$");
    
    /**
     * 月份格式化器
     */
    private static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM");
    
    /**
     * 扫描并获取所有月份目录
     * 对应10-list-months.bat的功能
     */
    public List<MonthInfo> scanMonths() {
        log.info("开始扫描月份目录: {}", migrateConfig.getSourceRoot());
        
        List<MonthInfo> monthList = new ArrayList<>();
        
        try {
            File sourceRoot = new File(migrateConfig.getSourceRoot());
            if (!sourceRoot.exists() || !sourceRoot.isDirectory()) {
                log.warn("源目录不存在或不是目录: {}", migrateConfig.getSourceRoot());
                return monthList;
            }
            
            File[] dirs = sourceRoot.listFiles(File::isDirectory);
            if (dirs == null) {
                log.warn("无法读取源目录内容: {}", migrateConfig.getSourceRoot());
                return monthList;
            }
            
            for (File dir : dirs) {
                String dirName = dir.getName();
                if (MONTH_PATTERN.matcher(dirName).matches()) {
                    MonthInfo monthInfo = createMonthInfo(dirName);
                    monthList.add(monthInfo);
                }
            }
            
            // 按月份排序
            monthList.sort(Comparator.comparing(MonthInfo::getMonth));
            
            log.info("扫描完成，找到{}个月份目录", monthList.size());
            
        } catch (Exception e) {
            log.error("扫描月份目录失败", e);
        }
        
        return monthList;
    }
    
    /**
     * 获取可迁移的月份列表（排除最近N个月）
     */
    public List<MonthInfo> getMigratableMonths() {
        List<MonthInfo> allMonths = scanMonths();
        
        if (migrateConfig.getExcludeRecentMonths() <= 0) {
            return allMonths;
        }
        
        // 计算排除的月份
        LocalDate currentDate = LocalDate.now();
        Set<String> excludeMonths = new HashSet<>();
        
        for (int i = 0; i < migrateConfig.getExcludeRecentMonths(); i++) {
            LocalDate excludeDate = currentDate.minusMonths(i);
            String excludeMonth = excludeDate.format(MONTH_FORMATTER);
            excludeMonths.add(excludeMonth);
        }
        
        // 过滤掉最近的月份
        List<MonthInfo> migratableMonths = allMonths.stream()
                .filter(month -> !excludeMonths.contains(month.getMonth()))
                .collect(Collectors.toList());
        
        log.info("排除最近{}个月后，可迁移月份数量: {}", migrateConfig.getExcludeRecentMonths(), migratableMonths.size());
        
        return migratableMonths;
    }
    
    /**
     * 获取指定月份的详细信息
     */
    public MonthInfo getMonthInfo(String month) {
        if (!MONTH_PATTERN.matcher(month).matches()) {
            throw new IllegalArgumentException("月份格式错误，应为yyyy-MM格式: " + month);
        }
        
        return createMonthInfo(month);
    }
    
    /**
     * 创建月份信息对象
     */
    private MonthInfo createMonthInfo(String month) {
        MonthInfo monthInfo = new MonthInfo();
        monthInfo.setMonth(month);
        monthInfo.setCreateTime(LocalDateTime.now());
        
        // 设置源目录路径
        String sourcePath = migrateConfig.getSourceRoot() + File.separator + month;
        monthInfo.setSourcePath(sourcePath);
        
        File sourceDir = new File(sourcePath);
        monthInfo.setSourceExists(sourceDir.exists() && sourceDir.isDirectory());
        
        if (monthInfo.isSourceExists()) {
            // 计算源目录大小
            monthInfo.setSourceSize(FileUtil.size(sourceDir));
            
            // 获取最后修改时间
            monthInfo.setLastModified(DateUtil.toLocalDateTime(new Date(sourceDir.lastModified())));
            
            // 检查是否为联接
            monthInfo.setJunction(SystemCommandUtil.isJunction(sourcePath));
            
            // 检查是否存在_old备份目录
            String oldBackupPath = sourcePath + "_old";
            monthInfo.setHasOldBackup(new File(oldBackupPath).exists());
        }
        
        // 确定目标路径和磁盘
        determineTargetPath(monthInfo);
        
        // 检查目标目录状态
        if (StrUtil.isNotBlank(monthInfo.getTargetPath())) {
            File targetDir = new File(monthInfo.getTargetPath());
            monthInfo.setTargetExists(targetDir.exists() && targetDir.isDirectory());
            
            if (monthInfo.isTargetExists()) {
                monthInfo.setTargetSize(FileUtil.size(targetDir));
            }
        }
        
        // 确定迁移状态
        determineStatus(monthInfo);
        
        return monthInfo;
    }
    
    /**
     * 确定目标路径和磁盘
     */
    private void determineTargetPath(MonthInfo monthInfo) {
        String month = monthInfo.getMonth();
        String strategy = migrateConfig.getStrategy();
        
        // 获取磁盘剩余空间
        long eFreeSpace = SystemCommandUtil.getDiskFreeSpaceGB("E:");
        long fFreeSpace = SystemCommandUtil.getDiskFreeSpaceGB("F:");
        
        String targetDrive = null;
        String targetPath = null;
        
        if ("BALANCE_BY_MONTH".equals(strategy)) {
            // 奇偶月均衡分配
            String[] parts = month.split("-");
            if (parts.length == 2) {
                int monthNum = Integer.parseInt(parts[1]);
                if (monthNum % 2 == 1) {
                    // 奇数月优先E盘
                    if (eFreeSpace > migrateConfig.getTargetEMinFreeGb()) {
                        targetDrive = "E:";
                        targetPath = migrateConfig.getTargetE() + File.separator + month;
                    } else if (fFreeSpace > migrateConfig.getTargetFMinFreeGb()) {
                        targetDrive = "F:";
                        targetPath = migrateConfig.getTargetF() + File.separator + month;
                    }
                } else {
                    // 偶数月优先F盘
                    if (fFreeSpace > migrateConfig.getTargetFMinFreeGb()) {
                        targetDrive = "F:";
                        targetPath = migrateConfig.getTargetF() + File.separator + month;
                    } else if (eFreeSpace > migrateConfig.getTargetEMinFreeGb()) {
                        targetDrive = "E:";
                        targetPath = migrateConfig.getTargetE() + File.separator + month;
                    }
                }
            }
        } else {
            // FILL_E_THEN_F策略：优先填满E盘
            if (eFreeSpace > migrateConfig.getTargetEMinFreeGb()) {
                targetDrive = "E:";
                targetPath = migrateConfig.getTargetE() + File.separator + month;
            } else if (fFreeSpace > migrateConfig.getTargetFMinFreeGb()) {
                targetDrive = "F:";
                targetPath = migrateConfig.getTargetF() + File.separator + month;
            }
        }
        
        monthInfo.setTargetDrive(targetDrive);
        monthInfo.setTargetPath(targetPath);
    }
    
    /**
     * 确定迁移状态
     */
    private void determineStatus(MonthInfo monthInfo) {
        if (!monthInfo.isSourceExists()) {
            monthInfo.setStatus(MonthInfo.MigrateStatus.ERROR);
            monthInfo.setRemark("源目录不存在");
            return;
        }
        
        if (monthInfo.isJunction()) {
            monthInfo.setStatus(MonthInfo.MigrateStatus.MIGRATED);
            monthInfo.setRemark("已创建联接");
        } else if (monthInfo.isTargetExists()) {
            monthInfo.setStatus(MonthInfo.MigrateStatus.PRE_SYNCED);
            monthInfo.setRemark("已预同步，等待切换");
        } else {
            monthInfo.setStatus(MonthInfo.MigrateStatus.NOT_MIGRATED);
            monthInfo.setRemark("未迁移");
        }
    }
    
    /**
     * 获取下个月的月份字符串
     */
    public String getNextMonth() {
        LocalDate nextMonth = LocalDate.now().plusMonths(1);
        return nextMonth.format(MONTH_FORMATTER);
    }
    
    /**
     * 获取月份统计信息
     */
    public Map<String, Object> getMonthStatistics() {
        List<MonthInfo> allMonths = scanMonths();
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalMonths", allMonths.size());
        
        long totalSourceSize = 0;
        long totalTargetSize = 0;
        int notMigratedCount = 0;
        int preSyncedCount = 0;
        int migratedCount = 0;
        int errorCount = 0;
        
        for (MonthInfo month : allMonths) {
            totalSourceSize += month.getSourceSize();
            totalTargetSize += month.getTargetSize();
            
            switch (month.getStatus()) {
                case NOT_MIGRATED:
                    notMigratedCount++;
                    break;
                case PRE_SYNCED:
                    preSyncedCount++;
                    break;
                case MIGRATED:
                    migratedCount++;
                    break;
                case ERROR:
                    errorCount++;
                    break;
            }
        }
        
        stats.put("totalSourceSize", totalSourceSize);
        stats.put("totalTargetSize", totalTargetSize);
        stats.put("notMigratedCount", notMigratedCount);
        stats.put("preSyncedCount", preSyncedCount);
        stats.put("migratedCount", migratedCount);
        stats.put("errorCount", errorCount);
        
        // 格式化大小
        stats.put("formattedSourceSize", formatSize(totalSourceSize));
        stats.put("formattedTargetSize", formatSize(totalTargetSize));
        
        return stats;
    }
    
    /**
     * 格式化文件大小
     */
    private String formatSize(long size) {
        if (size <= 0) return "0 B";
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        
        return String.format("%.2f %s", 
            size / Math.pow(1024, digitGroups), 
            units[digitGroups]);
    }
}
