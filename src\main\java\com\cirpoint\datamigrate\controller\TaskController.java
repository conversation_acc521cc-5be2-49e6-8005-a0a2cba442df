package com.cirpoint.datamigrate.controller;

import com.cirpoint.datamigrate.entity.MigrateTask;
import com.cirpoint.datamigrate.service.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务管理控制器
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
@RestController
@RequestMapping("/api/tasks")
public class TaskController {
    
    @Autowired
    private TaskService taskService;
    
    /**
     * 获取任务列表
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getTasks(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            List<MigrateTask> tasks = taskService.getTasks(page, size);
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", tasks);
            result.put("count", tasks.size());
            result.put("page", page);
            result.put("size", size);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务列表失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取任务列表失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取指定任务详情
     */
    @GetMapping("/{taskId}")
    public ResponseEntity<Map<String, Object>> getTask(@PathVariable String taskId) {
        try {
            MigrateTask task = taskService.getTask(taskId);
            Map<String, Object> result = new HashMap<>();
            
            if (task != null) {
                result.put("success", true);
                result.put("data", task);
            } else {
                result.put("success", false);
                result.put("message", "任务不存在: " + taskId);
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务详情失败: " + taskId, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取任务详情失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取正在运行的任务
     */
    @GetMapping("/running")
    public ResponseEntity<Map<String, Object>> getRunningTasks() {
        try {
            List<MigrateTask> tasks = taskService.getRunningTasks();
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", tasks);
            result.put("count", tasks.size());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取运行中任务失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取运行中任务失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取最近的任务
     */
    @GetMapping("/recent")
    public ResponseEntity<Map<String, Object>> getRecentTasks(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<MigrateTask> tasks = taskService.getRecentTasks(limit);
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", tasks);
            result.put("count", tasks.size());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取最近任务失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取最近任务失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 按类型获取任务
     */
    @GetMapping("/type/{taskType}")
    public ResponseEntity<Map<String, Object>> getTasksByType(@PathVariable String taskType) {
        try {
            MigrateTask.TaskType type;
            try {
                type = MigrateTask.TaskType.valueOf(taskType.toUpperCase());
            } catch (IllegalArgumentException e) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "无效的任务类型: " + taskType);
                return ResponseEntity.ok(result);
            }
            
            List<MigrateTask> tasks = taskService.getTasksByType(type);
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", tasks);
            result.put("count", tasks.size());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("按类型获取任务失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "按类型获取任务失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 取消任务
     */
    @PostMapping("/{taskId}/cancel")
    public ResponseEntity<Map<String, Object>> cancelTask(@PathVariable String taskId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = taskService.cancelTask(taskId);
            
            if (success) {
                result.put("success", true);
                result.put("message", "任务已取消");
            } else {
                result.put("success", false);
                result.put("message", "任务取消失败，可能任务不存在或已完成");
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("取消任务失败: " + taskId, e);
            result.put("success", false);
            result.put("message", "取消任务失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 删除任务
     */
    @DeleteMapping("/{taskId}")
    public ResponseEntity<Map<String, Object>> deleteTask(@PathVariable String taskId) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = taskService.deleteTask(taskId);
            
            if (success) {
                result.put("success", true);
                result.put("message", "任务已删除");
            } else {
                result.put("success", false);
                result.put("message", "任务删除失败，可能任务不存在或正在运行");
            }
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("删除任务失败: " + taskId, e);
            result.put("success", false);
            result.put("message", "删除任务失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 清理已完成的任务
     */
    @PostMapping("/cleanup")
    public ResponseEntity<Map<String, Object>> cleanupCompletedTasks(@RequestParam(defaultValue = "50") int keepCount) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            int deletedCount = taskService.cleanupCompletedTasks(keepCount);
            result.put("success", true);
            result.put("message", "清理完成，删除了 " + deletedCount + " 个任务");
            result.put("deletedCount", deletedCount);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("清理任务失败", e);
            result.put("success", false);
            result.put("message", "清理任务失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取任务统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getTaskStatistics() {
        try {
            Map<String, Object> statistics = taskService.getTaskStatistics();
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", statistics);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务统计失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取任务统计失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取月份的任务历史
     */
    @GetMapping("/history/{month}")
    public ResponseEntity<Map<String, Object>> getTaskHistory(@PathVariable String month) {
        try {
            List<MigrateTask> tasks = taskService.getTaskHistory(month);
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", tasks);
            result.put("count", tasks.size());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务历史失败: " + month, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取任务历史失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取任务类型选项
     */
    @GetMapping("/types")
    public ResponseEntity<Map<String, Object>> getTaskTypes() {
        try {
            Map<String, String> types = new HashMap<>();
            for (MigrateTask.TaskType type : MigrateTask.TaskType.values()) {
                types.put(type.name(), type.getName());
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", types);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务类型选项失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取任务类型选项失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取任务状态选项
     */
    @GetMapping("/statuses")
    public ResponseEntity<Map<String, Object>> getTaskStatuses() {
        try {
            Map<String, String> statuses = new HashMap<>();
            for (MigrateTask.TaskStatus status : MigrateTask.TaskStatus.values()) {
                statuses.put(status.name(), status.getName());
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", statuses);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务状态选项失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取任务状态选项失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
}
