package com.cirpoint.datamigrate.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.cirpoint.datamigrate.config.MigrateConfig;
import com.cirpoint.datamigrate.entity.MigrateTask;
import com.cirpoint.datamigrate.entity.MonthInfo;
import com.cirpoint.datamigrate.util.SystemCommandUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * 联接管理服务
 * 对应批处理脚本中的create_current_month_link.bat和50-autolink-schedule.bat功能
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
@Service
public class JunctionService {
    
    @Autowired
    private MigrateConfig migrateConfig;
    
    @Autowired
    private MonthService monthService;
    
    @Autowired
    private TaskService taskService;
    
    /**
     * 创建下个月的目录联接
     * 对应create_current_month_link.bat功能
     */
    public String createNextMonthLink(Consumer<String> progressCallback) {
        String nextMonth = getNextMonth();
        return createMonthLink(nextMonth, progressCallback);
    }
    
    /**
     * 创建指定月份的目录联接
     */
    public String createMonthLink(String month, Consumer<String> progressCallback) {
        log.info("开始创建月份联接: {}", month);
        
        MigrateTask task = new MigrateTask();
        task.setTaskId(IdUtil.simpleUUID());
        task.setTaskName("创建联接-" + month + "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss")));
        task.setTaskType(MigrateTask.TaskType.CREATE_LINK);
        task.setStatus(MigrateTask.TaskStatus.PENDING);
        task.setMonths(java.util.Arrays.asList(month));
        task.setCurrentMonth(month);
        task.setDryRun(migrateConfig.isDryRun());
        task.setCreator("wanghq");
        task.setStartTime(LocalDateTime.now());
        
        taskService.saveTask(task);
        
        CompletableFuture.runAsync(() -> {
            createMonthLinkInternal(task, month, progressCallback);
        });
        
        return task.getTaskId();
    }
    
    /**
     * 内部创建联接逻辑
     */
    private void createMonthLinkInternal(MigrateTask task, String month, Consumer<String> progressCallback) {
        try {
            task.setStatus(MigrateTask.TaskStatus.RUNNING);
            task.setCurrentProgress(0);
            taskService.updateTask(task);
            
            if (progressCallback != null) {
                progressCallback.accept("目标月份：" + month);
            }
            
            task.setCurrentProgress(10);
            taskService.updateTask(task);
            
            // 选择目标目录
            String targetPath = selectTargetPath();
            if (StrUtil.isBlank(targetPath)) {
                throw new RuntimeException("错误：无可用目标盘");
            }
            
            if (progressCallback != null) {
                progressCallback.accept("选择目标：" + targetPath);
            }
            
            task.setCurrentProgress(20);
            taskService.updateTask(task);
            
            // 确保目标月份目录存在
            String targetMonthPath = targetPath + File.separator + month;
            if (!new File(targetMonthPath).exists()) {
                if (progressCallback != null) {
                    progressCallback.accept("创建目标目录：" + targetMonthPath);
                }
                
                if (!migrateConfig.isDryRun()) {
                    FileUtil.mkdir(targetMonthPath);
                }
            }
            
            task.setCurrentProgress(40);
            taskService.updateTask(task);
            
            // 检查源目录状态
            String sourcePath = migrateConfig.getSourceRoot() + File.separator + month;
            File sourceDir = new File(sourcePath);
            
            if (sourceDir.exists()) {
                // 检查是否已经是联接
                if (SystemCommandUtil.isJunction(sourcePath)) {
                    if (progressCallback != null) {
                        progressCallback.accept("联接已存在：" + sourcePath + " -> " + targetMonthPath);
                    }
                    
                    task.setStatus(MigrateTask.TaskStatus.SUCCESS);
                    task.setEndTime(LocalDateTime.now());
                    task.setTotalProgress(100);
                    task.setCurrentProgress(100);
                    task.setResultDetail("联接已存在: " + month);
                    return;
                } else {
                    // 发现实体目录，需要先迁移数据
                    throw new RuntimeException("发现实体目录：" + sourcePath + "，需要先迁移数据。建议运行正式切换功能进行迁移");
                }
            }
            
            task.setCurrentProgress(60);
            taskService.updateTask(task);
            
            // 创建联接
            String linkCommand = "mklink /J \"" + sourcePath + "\" \"" + targetMonthPath + "\"";
            if (progressCallback != null) {
                progressCallback.accept("创建联接：" + sourcePath + " -> " + targetMonthPath);
                progressCallback.accept("[LINK] " + linkCommand);
            }
            
            if (!migrateConfig.isDryRun()) {
                SystemCommandUtil.CommandResult linkResult = SystemCommandUtil.executeCommand(linkCommand);
                if (!linkResult.isSuccess()) {
                    throw new RuntimeException("联接创建失败: " + linkResult.getErrorMessage());
                }
            }
            
            task.setCurrentProgress(90);
            taskService.updateTask(task);
            
            task.setStatus(MigrateTask.TaskStatus.SUCCESS);
            task.setEndTime(LocalDateTime.now());
            task.setTotalProgress(100);
            task.setCurrentProgress(100);
            task.setResultDetail("联接创建成功: " + month);
            
            if (progressCallback != null) {
                progressCallback.accept("联接创建成功");
            }
            
        } catch (Exception e) {
            log.error("创建联接失败: " + month, e);
            task.setStatus(MigrateTask.TaskStatus.FAILED);
            task.setEndTime(LocalDateTime.now());
            task.setErrorMessage(e.getMessage());
            
            if (progressCallback != null) {
                progressCallback.accept("创建联接失败: " + month + ", 错误: " + e.getMessage());
            }
        } finally {
            taskService.updateTask(task);
        }
    }
    
    /**
     * 选择目标路径
     */
    private String selectTargetPath() {
        // 检查E盘
        if (StrUtil.isNotBlank(migrateConfig.getTargetE())) {
            File targetEDir = new File(migrateConfig.getTargetE());
            if (targetEDir.exists() && targetEDir.isDirectory()) {
                return migrateConfig.getTargetE();
            }
        }
        
        // 检查F盘
        if (StrUtil.isNotBlank(migrateConfig.getTargetF())) {
            File targetFDir = new File(migrateConfig.getTargetF());
            if (targetFDir.exists() && targetFDir.isDirectory()) {
                return migrateConfig.getTargetF();
            }
        }
        
        return null;
    }
    
    /**
     * 获取下个月份字符串
     */
    private String getNextMonth() {
        LocalDate nextMonth = LocalDate.now().plusMonths(1);
        return nextMonth.format(DateTimeFormatter.ofPattern("yyyy-MM"));
    }
    
    /**
     * 删除联接
     */
    public boolean deleteJunction(String month) {
        try {
            String sourcePath = migrateConfig.getSourceRoot() + File.separator + month;
            
            if (!SystemCommandUtil.isJunction(sourcePath)) {
                log.warn("目录不是联接，无法删除: {}", sourcePath);
                return false;
            }
            
            if (migrateConfig.isDryRun()) {
                log.info("演练模式：删除联接 {}", sourcePath);
                return true;
            }
            
            String deleteCommand = "rmdir \"" + sourcePath + "\"";
            SystemCommandUtil.CommandResult result = SystemCommandUtil.executeCommand(deleteCommand);
            
            if (result.isSuccess()) {
                log.info("联接删除成功: {}", sourcePath);
                return true;
            } else {
                log.error("联接删除失败: {}, 错误: {}", sourcePath, result.getErrorMessage());
                return false;
            }
            
        } catch (Exception e) {
            log.error("删除联接异常: " + month, e);
            return false;
        }
    }
    
    /**
     * 验证联接状态
     */
    public Map<String, Object> validateJunction(String month) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String sourcePath = migrateConfig.getSourceRoot() + File.separator + month;
            File sourceDir = new File(sourcePath);
            
            result.put("month", month);
            result.put("sourcePath", sourcePath);
            result.put("sourceExists", sourceDir.exists());
            
            if (sourceDir.exists()) {
                boolean isJunction = SystemCommandUtil.isJunction(sourcePath);
                result.put("isJunction", isJunction);
                
                if (isJunction) {
                    // 尝试读取联接目标
                    String targetPath = getJunctionTarget(sourcePath);
                    result.put("targetPath", targetPath);
                    
                    if (StrUtil.isNotBlank(targetPath)) {
                        File targetDir = new File(targetPath);
                        result.put("targetExists", targetDir.exists());
                        result.put("targetAccessible", targetDir.canRead());
                        
                        if (targetDir.exists()) {
                            result.put("targetSize", FileUtil.size(targetDir));
                        }
                    }
                    
                    result.put("status", "VALID_JUNCTION");
                    result.put("description", "有效的联接");
                } else {
                    result.put("status", "REAL_DIRECTORY");
                    result.put("description", "实体目录（非联接）");
                    result.put("sourceSize", FileUtil.size(sourceDir));
                }
            } else {
                result.put("status", "NOT_EXISTS");
                result.put("description", "目录不存在");
            }
            
        } catch (Exception e) {
            log.error("验证联接状态失败: " + month, e);
            result.put("status", "ERROR");
            result.put("description", "验证失败: " + e.getMessage());
        }
        
        return result;
    }
    
    /**
     * 获取联接目标路径
     */
    private String getJunctionTarget(String junctionPath) {
        try {
            String command = "dir \"" + junctionPath + "\"";
            SystemCommandUtil.CommandResult result = SystemCommandUtil.executeCommand(command);
            
            if (result.isSuccess()) {
                String output = result.getOutput();
                String[] lines = output.split("\n");
                
                for (String line : lines) {
                    if (line.contains("<JUNCTION>") && line.contains("[")) {
                        int startIndex = line.indexOf("[");
                        int endIndex = line.indexOf("]", startIndex);
                        if (startIndex > 0 && endIndex > startIndex) {
                            return line.substring(startIndex + 1, endIndex);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取联接目标失败: " + junctionPath, e);
        }
        
        return null;
    }
    
    /**
     * 批量验证联接状态
     */
    public Map<String, Map<String, Object>> batchValidateJunctions(java.util.List<String> months) {
        Map<String, Map<String, Object>> results = new HashMap<>();
        
        for (String month : months) {
            results.put(month, validateJunction(month));
        }
        
        return results;
    }
    
    /**
     * 获取联接统计信息
     */
    public Map<String, Object> getJunctionStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        java.util.List<MonthInfo> allMonths = monthService.scanMonths();
        
        int totalMonths = allMonths.size();
        int junctionCount = 0;
        int realDirCount = 0;
        int notExistsCount = 0;
        
        for (MonthInfo month : allMonths) {
            if (month.isSourceExists()) {
                if (month.isJunction()) {
                    junctionCount++;
                } else {
                    realDirCount++;
                }
            } else {
                notExistsCount++;
            }
        }
        
        stats.put("totalMonths", totalMonths);
        stats.put("junctionCount", junctionCount);
        stats.put("realDirCount", realDirCount);
        stats.put("notExistsCount", notExistsCount);
        
        // 计算百分比
        if (totalMonths > 0) {
            stats.put("junctionPercentage", (junctionCount * 100.0) / totalMonths);
            stats.put("realDirPercentage", (realDirCount * 100.0) / totalMonths);
        } else {
            stats.put("junctionPercentage", 0.0);
            stats.put("realDirPercentage", 0.0);
        }
        
        return stats;
    }
}
