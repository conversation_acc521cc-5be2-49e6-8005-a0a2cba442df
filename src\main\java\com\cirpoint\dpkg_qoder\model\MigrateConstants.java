package com.cirpoint.dpkg_qoder.model;

/**
 * 数据迁移系统常量定义
 * 
 * <AUTHOR> IDE
 * @since 1.0.0
 */
public class MigrateConstants {
    
    /**
     * 配置文件名
     */
    public static final String CONFIG_FILE_NAME = "migrate-config.properties";
    
    /**
     * 默认配置文件路径
     */
    public static final String DEFAULT_CONFIG_PATH = "config/" + CONFIG_FILE_NAME;
    
    /**
     * 月份格式正则表达式
     */
    public static final String MONTH_PATTERN = "^[0-9]{4}-[0-9]{2}$";
    
    /**
     * 日志文件扩展名
     */
    public static final String LOG_FILE_EXTENSION = ".log";
    
    /**
     * 预同步日志文件前缀
     */
    public static final String PRE_SYNC_LOG_PREFIX = "pre_";
    
    /**
     * 最终同步日志文件前缀
     */
    public static final String FINAL_SYNC_LOG_PREFIX = "final_";
    
    /**
     * 验证日志文件前缀
     */
    public static final String VERIFY_LOG_PREFIX = "verify_";
    
    /**
     * 哈希校验日志文件前缀
     */
    public static final String HASH_LOG_PREFIX = "hash_";
    
    /**
     * 回滚日志文件前缀
     */
    public static final String ROLLBACK_LOG_PREFIX = "rollback_";
    
    /**
     * 旧目录后缀
     */
    public static final String OLD_DIR_SUFFIX = "_old";
    
    /**
     * GB转换常量
     */
    public static final long GB_SIZE = 1024L * 1024L * 1024L;
    
    /**
     * 默认缓冲区大小
     */
    public static final int DEFAULT_BUFFER_SIZE = 8192;
    
    /**
     * Windows系统命令
     */
    public static class WindowsCommands {
        /**
         * Robocopy命令
         */
        public static final String ROBOCOPY = "robocopy";
        
        /**
         * mklink命令
         */
        public static final String MKLINK = "mklink";
        
        /**
         * net stop命令
         */
        public static final String NET_STOP = "net stop";
        
        /**
         * net start命令
         */
        public static final String NET_START = "net start";
        
        /**
         * wmic命令
         */
        public static final String WMIC = "wmic";
        
        /**
         * certutil命令
         */
        public static final String CERTUTIL = "certutil";
        
        /**
         * rmdir命令
         */
        public static final String RMDIR = "rmdir";
        
        /**
         * move命令
         */
        public static final String MOVE = "move";
    }
    
    /**
     * Robocopy参数
     */
    public static class RobocopyOptions {
        /**
         * 复制子目录包括空目录
         */
        public static final String COPY_SUBDIRS_INCLUDING_EMPTY = "/E";
        
        /**
         * 镜像模式（删除目标多余文件）
         */
        public static final String MIRROR = "/MIR";
        
        /**
         * 复制选项：数据、属性、时间戳、安全信息、所有者
         */
        public static final String COPY_ALL = "/COPY:DATSO";
        
        /**
         * 目录复制选项
         */
        public static final String DCOPY = "/DCOPY:DAT";
        
        /**
         * 多线程参数前缀
         */
        public static final String MULTI_THREAD = "/MT:";
        
        /**
         * 重试次数参数前缀
         */
        public static final String RETRY = "/R:";
        
        /**
         * 重试等待时间参数前缀
         */
        public static final String WAIT = "/W:";
        
        /**
         * 容错文件时间
         */
        public static final String ASSUME_FAT_FILE_TIMES = "/FFT";
        
        /**
         * 排除联接点
         */
        public static final String EXCLUDE_JUNCTION_POINTS = "/XJ";
        
        /**
         * 输出到控制台
         */
        public static final String TEE_OUTPUT = "/TEE";
        
        /**
         * 输出日志
         */
        public static final String LOG = "/LOG+:";
        
        /**
         * 列表模式（不复制）
         */
        public static final String LIST_ONLY = "/L";
        
        /**
         * 无作业头
         */
        public static final String NO_JOB_HEADER = "/NJH";
        
        /**
         * 无作业摘要
         */
        public static final String NO_JOB_SUMMARY = "/NJS";
        
        /**
         * 无进度
         */
        public static final String NO_PROGRESS = "/NP";
        
        /**
         * 无目录列表
         */
        public static final String NO_DIRECTORY_LIST = "/NDL";
        
        /**
         * 无文件列表
         */
        public static final String NO_FILE_LIST = "/NFL";
    }
    
    /**
     * 操作状态枚举
     */
    public enum OperationStatus {
        /**
         * 等待中
         */
        PENDING,
        
        /**
         * 进行中
         */
        RUNNING,
        
        /**
         * 已完成
         */
        COMPLETED,
        
        /**
         * 失败
         */
        FAILED,
        
        /**
         * 已取消
         */
        CANCELLED
    }
    
    /**
     * 操作类型枚举
     */
    public enum OperationType {
        /**
         * 预同步
         */
        PRE_SYNC,
        
        /**
         * 最终同步
         */
        FINAL_SYNC,
        
        /**
         * 创建联接
         */
        CREATE_JUNCTION,
        
        /**
         * 回滚
         */
        ROLLBACK,
        
        /**
         * 验证
         */
        VERIFY,
        
        /**
         * 哈希校验
         */
        HASH_VERIFY
    }
}