package com.cirpoint.dpkg_qoder.model;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 操作结果封装类
 * 
 * <AUTHOR> IDE
 * @since 1.0.0
 */
@Data
public class OperationResult<T> {
    
    /**
     * 操作是否成功
     */
    private boolean success;
    
    /**
     * 结果数据
     */
    private T data;
    
    /**
     * 消息
     */
    private String message;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 操作时间
     */
    private LocalDateTime timestamp;
    
    /**
     * 详细日志信息
     */
    private List<String> logs;
    
    public OperationResult() {
        this.timestamp = LocalDateTime.now();
    }
    
    public OperationResult(boolean success, T data, String message) {
        this();
        this.success = success;
        this.data = data;
        this.message = message;
    }
    
    /**
     * 创建成功结果
     */
    public static <T> OperationResult<T> success(T data) {
        return new OperationResult<>(true, data, "操作成功");
    }
    
    /**
     * 创建成功结果并携带消息
     */
    public static <T> OperationResult<T> success(T data, String message) {
        return new OperationResult<>(true, data, message);
    }
    
    /**
     * 创建失败结果
     */
    public static <T> OperationResult<T> failure(String message) {
        return new OperationResult<>(false, null, message);
    }
    
    /**
     * 创建失败结果并携带错误代码
     */
    public static <T> OperationResult<T> failure(String message, String errorCode) {
        OperationResult<T> result = new OperationResult<>(false, null, message);
        result.setErrorCode(errorCode);
        return result;
    }
}

/**
 * 迁移操作详情
 */
@Data
class MigrateOperation {
    
    /**
     * 操作ID
     */
    private String operationId;
    
    /**
     * 操作类型
     */
    private MigrateConstants.OperationType type;
    
    /**
     * 操作状态
     */
    private MigrateConstants.OperationStatus status;
    
    /**
     * 月份
     */
    private String month;
    
    /**
     * 源路径
     */
    private String sourcePath;
    
    /**
     * 目标路径
     */
    private String targetPath;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 进度百分比
     */
    private Integer progress;
    
    /**
     * 消息
     */
    private String message;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 日志文件路径
     */
    private String logFilePath;
}

/**
 * 磁盘空间信息
 */
@Data
class DiskSpaceInfo {
    
    /**
     * 磁盘标识（如 E:, F:）
     */
    private String drive;
    
    /**
     * 总容量（GB）
     */
    private Long totalSpaceGb;
    
    /**
     * 可用空间（GB）
     */
    private Long freeSpaceGb;
    
    /**
     * 已用空间（GB）
     */
    private Long usedSpaceGb;
    
    /**
     * 使用率百分比
     */
    private Double usagePercentage;
    
    /**
     * 是否有足够空间
     */
    private Boolean hasEnoughSpace;
}