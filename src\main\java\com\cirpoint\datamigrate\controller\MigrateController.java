package com.cirpoint.datamigrate.controller;

import com.cirpoint.datamigrate.service.JunctionService;
import com.cirpoint.datamigrate.service.RollbackService;
import com.cirpoint.datamigrate.service.SyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 迁移操作控制器
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
@RestController
@RequestMapping("/api/migrate")
public class MigrateController {
    
    @Autowired
    private SyncService syncService;
    
    @Autowired
    private RollbackService rollbackService;
    
    @Autowired
    private JunctionService junctionService;
    
    /**
     * 执行预同步
     */
    @PostMapping("/pre-sync")
    public ResponseEntity<Map<String, Object>> executePreSync(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            @SuppressWarnings("unchecked")
            List<String> months = (List<String>) request.get("months");
            
            if (months == null || months.isEmpty()) {
                result.put("success", false);
                result.put("message", "请选择要预同步的月份");
                return ResponseEntity.ok(result);
            }
            
            String taskId = syncService.executePreSync(months, null);
            
            result.put("success", true);
            result.put("message", "预同步任务已启动");
            result.put("taskId", taskId);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("启动预同步失败", e);
            result.put("success", false);
            result.put("message", "启动预同步失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 执行正式切换
     */
    @PostMapping("/cutover")
    public ResponseEntity<Map<String, Object>> executeCutover(@RequestBody Map<String, Object> request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            @SuppressWarnings("unchecked")
            List<String> months = (List<String>) request.get("months");
            
            if (months == null || months.isEmpty()) {
                result.put("success", false);
                result.put("message", "请选择要切换的月份");
                return ResponseEntity.ok(result);
            }
            
            String taskId = syncService.executeCutover(months, null);
            
            result.put("success", true);
            result.put("message", "正式切换任务已启动");
            result.put("taskId", taskId);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("启动正式切换失败", e);
            result.put("success", false);
            result.put("message", "启动正式切换失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 执行回滚
     */
    @PostMapping("/rollback/{month}")
    public ResponseEntity<Map<String, Object>> executeRollback(@PathVariable String month) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (!rollbackService.canRollback(month)) {
                result.put("success", false);
                result.put("message", "月份 " + month + " 不支持回滚");
                return ResponseEntity.ok(result);
            }
            
            String taskId = rollbackService.executeRollback(month, null);
            
            result.put("success", true);
            result.put("message", "回滚任务已启动");
            result.put("taskId", taskId);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("启动回滚失败: " + month, e);
            result.put("success", false);
            result.put("message", "启动回滚失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取回滚状态
     */
    @GetMapping("/rollback/{month}/status")
    public ResponseEntity<Map<String, Object>> getRollbackStatus(@PathVariable String month) {
        try {
            Map<String, Object> status = rollbackService.getRollbackStatus(month);
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", status);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取回滚状态失败: " + month, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取回滚状态失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 创建下个月联接
     */
    @PostMapping("/junction/next-month")
    public ResponseEntity<Map<String, Object>> createNextMonthJunction() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String taskId = junctionService.createNextMonthLink(null);
            
            result.put("success", true);
            result.put("message", "创建下个月联接任务已启动");
            result.put("taskId", taskId);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("创建下个月联接失败", e);
            result.put("success", false);
            result.put("message", "创建下个月联接失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 创建指定月份联接
     */
    @PostMapping("/junction/{month}")
    public ResponseEntity<Map<String, Object>> createMonthJunction(@PathVariable String month) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String taskId = junctionService.createMonthLink(month, null);
            
            result.put("success", true);
            result.put("message", "创建联接任务已启动");
            result.put("taskId", taskId);
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("创建联接失败: " + month, e);
            result.put("success", false);
            result.put("message", "创建联接失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 删除联接
     */
    @DeleteMapping("/junction/{month}")
    public ResponseEntity<Map<String, Object>> deleteJunction(@PathVariable String month) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = junctionService.deleteJunction(month);
            
            if (success) {
                result.put("success", true);
                result.put("message", "联接删除成功");
            } else {
                result.put("success", false);
                result.put("message", "联接删除失败");
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("删除联接失败: " + month, e);
            result.put("success", false);
            result.put("message", "删除联接失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 验证联接状态
     */
    @GetMapping("/junction/{month}/validate")
    public ResponseEntity<Map<String, Object>> validateJunction(@PathVariable String month) {
        try {
            Map<String, Object> validation = junctionService.validateJunction(month);
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", validation);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("验证联接失败: " + month, e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "验证联接失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 批量验证联接状态
     */
    @PostMapping("/junction/batch-validate")
    public ResponseEntity<Map<String, Object>> batchValidateJunctions(@RequestBody Map<String, Object> request) {
        try {
            @SuppressWarnings("unchecked")
            List<String> months = (List<String>) request.get("months");
            
            if (months == null || months.isEmpty()) {
                Map<String, Object> result = new HashMap<>();
                result.put("success", false);
                result.put("message", "请选择要验证的月份");
                return ResponseEntity.ok(result);
            }
            
            Map<String, Map<String, Object>> validations = junctionService.batchValidateJunctions(months);
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", validations);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("批量验证联接失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "批量验证联接失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取联接统计信息
     */
    @GetMapping("/junction/statistics")
    public ResponseEntity<Map<String, Object>> getJunctionStatistics() {
        try {
            Map<String, Object> statistics = junctionService.getJunctionStatistics();
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", statistics);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取联接统计失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取联接统计失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
}
