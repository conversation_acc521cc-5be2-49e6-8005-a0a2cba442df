package com.cirpoint.datamigrate.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 数据迁移配置类
 * 对应批处理脚本中的01-config.bat配置项
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Data
@Component
@ConfigurationProperties(prefix = "migrate")
public class MigrateConfig {
    
    /**
     * 源目录路径
     */
    private String sourceRoot = "D:\\DataPkgFile";
    
    /**
     * E盘目标目录
     */
    private String targetE = "E:\\DataPkgFile";
    
    /**
     * F盘目标目录
     */
    private String targetF = "F:\\DataPkgFile";
    
    /**
     * 日志目录
     */
    private String logDir = "D:\\eclipse-work\\DataPkg-Migrate\\logs";
    
    /**
     * 服务名称（可选）
     */
    private String serviceName = "";
    
    /**
     * 分配策略：FILL_E_THEN_F 或 BALANCE_BY_MONTH
     */
    private String strategy = "FILL_E_THEN_F";
    
    /**
     * E盘最小预留空间（GB）
     */
    private int targetEMinFreeGb = 50;
    
    /**
     * F盘最小预留空间（GB）
     */
    private int targetFMinFreeGb = 50;
    
    /**
     * 预同步排除最近N个月
     */
    private int excludeRecentMonths = 2;
    
    /**
     * 多线程数量
     */
    private int threads = 16;
    
    /**
     * Robocopy重试次数
     */
    private int roboRetry = 1;
    
    /**
     * Robocopy等待时间（秒）
     */
    private int roboWait = 2;
    
    /**
     * 安全开关：true=演练模式（只打印命令），false=执行模式
     */
    private boolean dryRun = true;
    
    /**
     * 切换后是否删除_old目录：true=删除，false=保留
     */
    private boolean deleteOld = false;
    
    /**
     * 抽样验证文件数量（每月抽N个文件哈希校验，0表示跳过）
     */
    private int verifySampleCount = 5;
    
    /**
     * 分配策略枚举
     */
    public enum Strategy {
        FILL_E_THEN_F("FILL_E_THEN_F", "优先填满E盘"),
        BALANCE_BY_MONTH("BALANCE_BY_MONTH", "奇偶月均衡分配");
        
        private final String code;
        private final String description;
        
        Strategy(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
        
        public static Strategy fromCode(String code) {
            for (Strategy strategy : values()) {
                if (strategy.code.equals(code)) {
                    return strategy;
                }
            }
            return FILL_E_THEN_F;
        }
    }
}
