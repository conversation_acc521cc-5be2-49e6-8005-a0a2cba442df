package com.cirpoint.datamigrate.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.cirpoint.datamigrate.config.MigrateConfig;
import com.cirpoint.datamigrate.entity.MigrateTask;
import com.cirpoint.datamigrate.entity.MonthInfo;
import com.cirpoint.datamigrate.util.SystemCommandUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * 回滚服务
 * 对应批处理脚本中的40-rollback-month.bat功能
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
@Service
public class RollbackService {
    
    @Autowired
    private MigrateConfig migrateConfig;
    
    @Autowired
    private MonthService monthService;
    
    @Autowired
    private TaskService taskService;
    
    /**
     * 执行回滚操作
     * 对应40-rollback-month.bat功能
     * 
     * @param month 要回滚的月份
     * @param progressCallback 进度回调
     * @return 任务ID
     */
    public String executeRollback(String month, Consumer<String> progressCallback) {
        log.info("开始执行回滚操作，月份: {}", month);
        
        // 创建任务
        MigrateTask task = new MigrateTask();
        task.setTaskId(IdUtil.simpleUUID());
        task.setTaskName("回滚-" + month + "-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss")));
        task.setTaskType(MigrateTask.TaskType.ROLLBACK);
        task.setStatus(MigrateTask.TaskStatus.PENDING);
        task.setMonths(java.util.Arrays.asList(month));
        task.setCurrentMonth(month);
        task.setDryRun(migrateConfig.isDryRun());
        task.setCreator("wanghq");
        task.setStartTime(LocalDateTime.now());
        
        taskService.saveTask(task);
        
        // 异步执行
        CompletableFuture.runAsync(() -> {
            executeRollbackInternal(task, month, progressCallback);
        });
        
        return task.getTaskId();
    }
    
    /**
     * 内部回滚执行逻辑
     */
    private void executeRollbackInternal(MigrateTask task, String month, Consumer<String> progressCallback) {
        try {
            task.setStatus(MigrateTask.TaskStatus.RUNNING);
            task.setCurrentProgress(0);
            taskService.updateTask(task);
            
            // 停止服务（如果配置了）
            if (StrUtil.isNotBlank(migrateConfig.getServiceName())) {
                if (progressCallback != null) {
                    progressCallback.accept("尝试停止服务：" + migrateConfig.getServiceName());
                }
                
                if (!migrateConfig.isDryRun()) {
                    String stopCommand = "net stop \"" + migrateConfig.getServiceName() + "\"";
                    SystemCommandUtil.executeCommand(stopCommand);
                }
            }
            
            task.setCurrentProgress(10);
            taskService.updateTask(task);
            
            // 获取月份信息
            MonthInfo monthInfo = monthService.getMonthInfo(month);
            String sourcePath = monthInfo.getSourcePath();
            String oldPath = sourcePath + "_old";
            
            if (new File(oldPath).exists()) {
                // 快速回滚：使用_old目录
                executeQuickRollback(task, sourcePath, oldPath, progressCallback);
            } else {
                // 深度回滚：从目标目录复制回来
                executeDeepRollback(task, monthInfo, progressCallback);
            }
            
            // 启动服务（如果配置了）
            if (StrUtil.isNotBlank(migrateConfig.getServiceName())) {
                if (progressCallback != null) {
                    progressCallback.accept("尝试启动服务：" + migrateConfig.getServiceName());
                }
                
                if (!migrateConfig.isDryRun()) {
                    String startCommand = "net start \"" + migrateConfig.getServiceName() + "\"";
                    SystemCommandUtil.executeCommand(startCommand);
                }
            }
            
            task.setStatus(MigrateTask.TaskStatus.SUCCESS);
            task.setEndTime(LocalDateTime.now());
            task.setTotalProgress(100);
            task.setCurrentProgress(100);
            task.setResultDetail("回滚完成: " + month);
            
            if (progressCallback != null) {
                progressCallback.accept("回滚完成: " + month);
            }
            
        } catch (Exception e) {
            log.error("回滚执行失败: " + month, e);
            task.setStatus(MigrateTask.TaskStatus.FAILED);
            task.setEndTime(LocalDateTime.now());
            task.setErrorMessage(e.getMessage());
            
            if (progressCallback != null) {
                progressCallback.accept("回滚执行失败: " + month + ", 错误: " + e.getMessage());
            }
        } finally {
            taskService.updateTask(task);
        }
    }
    
    /**
     * 执行快速回滚（使用_old目录）
     */
    private void executeQuickRollback(MigrateTask task, String sourcePath, String oldPath, Consumer<String> progressCallback) {
        if (progressCallback != null) {
            progressCallback.accept("执行快速回滚，使用备份目录: " + oldPath);
        }
        
        task.setCurrentProgress(30);
        taskService.updateTask(task);
        
        // 删除联接
        if (progressCallback != null) {
            progressCallback.accept("删除联接：" + sourcePath);
        }
        
        if (!migrateConfig.isDryRun()) {
            // 使用rmdir删除联接，不会删除目标内容
            String deleteCommand = "rmdir /S /Q \"" + sourcePath + "\"";
            SystemCommandUtil.CommandResult deleteResult = SystemCommandUtil.executeCommand(deleteCommand);
            if (!deleteResult.isSuccess()) {
                log.warn("删除联接失败，可能不是联接: {}", deleteResult.getErrorMessage());
            }
        }
        
        task.setCurrentProgress(60);
        taskService.updateTask(task);
        
        // 还原旧目录
        if (progressCallback != null) {
            progressCallback.accept("还原旧目录：" + oldPath + " -> " + sourcePath);
        }
        
        if (!migrateConfig.isDryRun()) {
            File oldDir = new File(oldPath);
            File sourceDir = new File(sourcePath);
            
            if (!oldDir.renameTo(sourceDir)) {
                throw new RuntimeException("还原目录失败: " + oldPath + " -> " + sourcePath);
            }
        }
        
        task.setCurrentProgress(90);
        taskService.updateTask(task);
    }
    
    /**
     * 执行深度回滚（从目标目录复制回来）
     */
    private void executeDeepRollback(MigrateTask task, MonthInfo monthInfo, Consumer<String> progressCallback) {
        if (progressCallback != null) {
            progressCallback.accept("未找到_old目录，将从目标复制回源（耗时较长）");
        }
        
        task.setCurrentProgress(20);
        taskService.updateTask(task);
        
        // 查找目标目录
        String targetPath = findTargetPath(monthInfo.getMonth());
        if (StrUtil.isBlank(targetPath)) {
            throw new RuntimeException("在E:和F:盘都未找到" + monthInfo.getMonth() + "目录");
        }
        
        if (progressCallback != null) {
            progressCallback.accept("找到目标目录：" + targetPath);
        }
        
        task.setCurrentProgress(30);
        taskService.updateTask(task);
        
        // 删除现有的联接或目录
        if (!migrateConfig.isDryRun()) {
            String deleteCommand = "rmdir \"" + monthInfo.getSourcePath() + "\"";
            SystemCommandUtil.executeCommand(deleteCommand);
        }
        
        task.setCurrentProgress(40);
        taskService.updateTask(task);
        
        // 从目标复制回源
        String logFile = getLogFilePath("rollback", monthInfo.getMonth());
        String copyCommand = buildRobocopyCommand(targetPath, monthInfo.getSourcePath(), logFile);
        
        if (progressCallback != null) {
            progressCallback.accept("[ROLLBACK] " + copyCommand);
        }
        
        if (!migrateConfig.isDryRun()) {
            SystemCommandUtil.CommandResult copyResult = SystemCommandUtil.executeCommand(
                copyCommand, null, 120, // 2小时超时
                line -> {
                    if (progressCallback != null) {
                        progressCallback.accept(line);
                    }
                }
            );
            
            if (!copyResult.isSuccess() && copyResult.getExitCode() > 7) {
                throw new RuntimeException("回滚复制失败: " + copyResult.getErrorMessage());
            }
        }
        
        task.setCurrentProgress(90);
        taskService.updateTask(task);
    }
    
    /**
     * 查找目标路径
     */
    private String findTargetPath(String month) {
        // 先检查E盘
        String ePath = migrateConfig.getTargetE() + File.separator + month;
        if (new File(ePath).exists()) {
            return ePath;
        }
        
        // 再检查F盘
        String fPath = migrateConfig.getTargetF() + File.separator + month;
        if (new File(fPath).exists()) {
            return fPath;
        }
        
        return null;
    }
    
    /**
     * 构建Robocopy命令
     */
    private String buildRobocopyCommand(String sourcePath, String targetPath, String logFile) {
        StringBuilder cmd = new StringBuilder();
        cmd.append("robocopy \"").append(sourcePath).append("\" \"").append(targetPath).append("\"");
        cmd.append(" /MIR"); // 镜像模式
        cmd.append(" /COPY:DATSO"); // 复制数据、属性、时间戳、安全、所有者
        cmd.append(" /DCOPY:DAT"); // 目录复制数据、属性、时间戳
        cmd.append(" /R:").append(migrateConfig.getRoboRetry()); // 重试次数
        cmd.append(" /W:").append(migrateConfig.getRoboWait()); // 等待时间
        cmd.append(" /MT:").append(migrateConfig.getThreads()); // 多线程
        cmd.append(" /FFT"); // 假设FAT文件时间（2秒精度）
        cmd.append(" /XJ"); // 排除联接点
        cmd.append(" /TEE"); // 输出到控制台和日志文件
        cmd.append(" /LOG+:\"").append(logFile).append("\""); // 追加日志
        
        return cmd.toString();
    }
    
    /**
     * 获取日志文件路径
     */
    private String getLogFilePath(String prefix, String month) {
        String logDir = migrateConfig.getLogDir();
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd-HHmmss"));
        return logDir + File.separator + prefix + "_" + month + "_" + timestamp + ".log";
    }
    
    /**
     * 检查月份是否可以回滚
     */
    public boolean canRollback(String month) {
        try {
            MonthInfo monthInfo = monthService.getMonthInfo(month);
            return monthInfo.canRollback();
        } catch (Exception e) {
            log.error("检查回滚状态失败: " + month, e);
            return false;
        }
    }
    
    /**
     * 获取回滚状态信息
     */
    public java.util.Map<String, Object> getRollbackStatus(String month) {
        java.util.Map<String, Object> status = new java.util.HashMap<>();
        
        try {
            MonthInfo monthInfo = monthService.getMonthInfo(month);
            
            status.put("month", month);
            status.put("canRollback", monthInfo.canRollback());
            status.put("isJunction", monthInfo.isJunction());
            status.put("hasOldBackup", monthInfo.isHasOldBackup());
            status.put("sourceExists", monthInfo.isSourceExists());
            
            if (monthInfo.isHasOldBackup()) {
                status.put("rollbackType", "QUICK");
                status.put("rollbackDescription", "快速回滚（使用_old备份目录）");
            } else if (monthInfo.isJunction()) {
                String targetPath = findTargetPath(month);
                if (StrUtil.isNotBlank(targetPath)) {
                    status.put("rollbackType", "DEEP");
                    status.put("rollbackDescription", "深度回滚（从目标目录复制回来，耗时较长）");
                    status.put("targetPath", targetPath);
                } else {
                    status.put("rollbackType", "IMPOSSIBLE");
                    status.put("rollbackDescription", "无法回滚（未找到目标目录）");
                }
            } else {
                status.put("rollbackType", "NONE");
                status.put("rollbackDescription", "无需回滚（未迁移）");
            }
            
        } catch (Exception e) {
            log.error("获取回滚状态失败: " + month, e);
            status.put("error", e.getMessage());
        }
        
        return status;
    }
}
