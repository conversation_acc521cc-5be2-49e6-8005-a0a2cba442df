package com.cirpoint.datamigrate.service;

import cn.hutool.core.util.IdUtil;
import com.cirpoint.datamigrate.entity.MigrateTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 任务管理服务
 * 管理迁移任务的生命周期
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
@Service
public class TaskService {
    
    /**
     * 任务存储（内存存储，实际项目中应使用数据库）
     */
    private final Map<String, MigrateTask> taskStorage = new ConcurrentHashMap<>();
    
    /**
     * 保存任务
     */
    public void saveTask(MigrateTask task) {
        if (task.getTaskId() == null) {
            task.setTaskId(IdUtil.simpleUUID());
        }
        taskStorage.put(task.getTaskId(), task);
        log.info("保存任务: {}", task.getTaskId());
    }
    
    /**
     * 更新任务
     */
    public void updateTask(MigrateTask task) {
        if (task.getTaskId() != null && taskStorage.containsKey(task.getTaskId())) {
            taskStorage.put(task.getTaskId(), task);
        }
    }
    
    /**
     * 根据ID获取任务
     */
    public MigrateTask getTask(String taskId) {
        return taskStorage.get(taskId);
    }
    
    /**
     * 获取所有任务
     */
    public List<MigrateTask> getAllTasks() {
        return new ArrayList<>(taskStorage.values());
    }
    
    /**
     * 获取任务列表（分页）
     */
    public List<MigrateTask> getTasks(int page, int size) {
        List<MigrateTask> allTasks = getAllTasks();
        
        // 按开始时间倒序排序
        allTasks.sort((t1, t2) -> {
            LocalDateTime time1 = t1.getStartTime() != null ? t1.getStartTime() : LocalDateTime.MIN;
            LocalDateTime time2 = t2.getStartTime() != null ? t2.getStartTime() : LocalDateTime.MIN;
            return time2.compareTo(time1);
        });
        
        int start = (page - 1) * size;
        int end = Math.min(start + size, allTasks.size());
        
        if (start >= allTasks.size()) {
            return new ArrayList<>();
        }
        
        return allTasks.subList(start, end);
    }
    
    /**
     * 获取正在运行的任务
     */
    public List<MigrateTask> getRunningTasks() {
        return taskStorage.values().stream()
                .filter(task -> task.getStatus() == MigrateTask.TaskStatus.RUNNING)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取指定类型的任务
     */
    public List<MigrateTask> getTasksByType(MigrateTask.TaskType taskType) {
        return taskStorage.values().stream()
                .filter(task -> task.getTaskType() == taskType)
                .sorted((t1, t2) -> {
                    LocalDateTime time1 = t1.getStartTime() != null ? t1.getStartTime() : LocalDateTime.MIN;
                    LocalDateTime time2 = t2.getStartTime() != null ? t2.getStartTime() : LocalDateTime.MIN;
                    return time2.compareTo(time1);
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 获取最近的任务
     */
    public List<MigrateTask> getRecentTasks(int limit) {
        return taskStorage.values().stream()
                .sorted((t1, t2) -> {
                    LocalDateTime time1 = t1.getStartTime() != null ? t1.getStartTime() : LocalDateTime.MIN;
                    LocalDateTime time2 = t2.getStartTime() != null ? t2.getStartTime() : LocalDateTime.MIN;
                    return time2.compareTo(time1);
                })
                .limit(limit)
                .collect(Collectors.toList());
    }
    
    /**
     * 删除任务
     */
    public boolean deleteTask(String taskId) {
        MigrateTask task = taskStorage.get(taskId);
        if (task != null) {
            if (task.isRunning()) {
                log.warn("无法删除正在运行的任务: {}", taskId);
                return false;
            }
            taskStorage.remove(taskId);
            log.info("删除任务: {}", taskId);
            return true;
        }
        return false;
    }
    
    /**
     * 取消任务
     */
    public boolean cancelTask(String taskId) {
        MigrateTask task = taskStorage.get(taskId);
        if (task != null && task.isRunning()) {
            task.setStatus(MigrateTask.TaskStatus.CANCELLED);
            task.setEndTime(LocalDateTime.now());
            task.setErrorMessage("任务被用户取消");
            updateTask(task);
            log.info("取消任务: {}", taskId);
            return true;
        }
        return false;
    }
    
    /**
     * 清理已完成的任务（保留最近N个）
     */
    public int cleanupCompletedTasks(int keepCount) {
        List<MigrateTask> completedTasks = taskStorage.values().stream()
                .filter(MigrateTask::isCompleted)
                .sorted((t1, t2) -> {
                    LocalDateTime time1 = t1.getEndTime() != null ? t1.getEndTime() : LocalDateTime.MIN;
                    LocalDateTime time2 = t2.getEndTime() != null ? t2.getEndTime() : LocalDateTime.MIN;
                    return time2.compareTo(time1);
                })
                .collect(Collectors.toList());
        
        int deletedCount = 0;
        if (completedTasks.size() > keepCount) {
            List<MigrateTask> tasksToDelete = completedTasks.subList(keepCount, completedTasks.size());
            for (MigrateTask task : tasksToDelete) {
                taskStorage.remove(task.getTaskId());
                deletedCount++;
            }
        }
        
        log.info("清理已完成任务，删除{}个任务", deletedCount);
        return deletedCount;
    }
    
    /**
     * 获取任务统计信息
     */
    public Map<String, Object> getTaskStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        List<MigrateTask> allTasks = getAllTasks();
        stats.put("totalTasks", allTasks.size());
        
        long runningCount = allTasks.stream().filter(MigrateTask::isRunning).count();
        long successCount = allTasks.stream().filter(t -> t.getStatus() == MigrateTask.TaskStatus.SUCCESS).count();
        long failedCount = allTasks.stream().filter(t -> t.getStatus() == MigrateTask.TaskStatus.FAILED).count();
        long cancelledCount = allTasks.stream().filter(t -> t.getStatus() == MigrateTask.TaskStatus.CANCELLED).count();
        long pendingCount = allTasks.stream().filter(t -> t.getStatus() == MigrateTask.TaskStatus.PENDING).count();
        
        stats.put("runningCount", runningCount);
        stats.put("successCount", successCount);
        stats.put("failedCount", failedCount);
        stats.put("cancelledCount", cancelledCount);
        stats.put("pendingCount", pendingCount);
        
        // 按任务类型统计
        Map<String, Long> typeStats = allTasks.stream()
                .collect(Collectors.groupingBy(
                    task -> task.getTaskType().name(),
                    Collectors.counting()
                ));
        stats.put("typeStatistics", typeStats);
        
        // 最近24小时的任务数量
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
        long recentTaskCount = allTasks.stream()
                .filter(task -> task.getStartTime() != null && task.getStartTime().isAfter(yesterday))
                .count();
        stats.put("recentTaskCount", recentTaskCount);
        
        return stats;
    }
    
    /**
     * 检查是否有正在运行的任务
     */
    public boolean hasRunningTasks() {
        return taskStorage.values().stream().anyMatch(MigrateTask::isRunning);
    }
    
    /**
     * 检查指定类型的任务是否正在运行
     */
    public boolean hasRunningTaskOfType(MigrateTask.TaskType taskType) {
        return taskStorage.values().stream()
                .anyMatch(task -> task.getTaskType() == taskType && task.isRunning());
    }
    
    /**
     * 获取任务执行历史
     */
    public List<MigrateTask> getTaskHistory(String month) {
        return taskStorage.values().stream()
                .filter(task -> task.getMonths() != null && task.getMonths().contains(month))
                .sorted((t1, t2) -> {
                    LocalDateTime time1 = t1.getStartTime() != null ? t1.getStartTime() : LocalDateTime.MIN;
                    LocalDateTime time2 = t2.getStartTime() != null ? t2.getStartTime() : LocalDateTime.MIN;
                    return time2.compareTo(time1);
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 创建简单任务
     */
    public MigrateTask createSimpleTask(MigrateTask.TaskType taskType, String taskName, List<String> months) {
        MigrateTask task = new MigrateTask();
        task.setTaskId(IdUtil.simpleUUID());
        task.setTaskName(taskName);
        task.setTaskType(taskType);
        task.setStatus(MigrateTask.TaskStatus.PENDING);
        task.setMonths(months);
        task.setCreator("wanghq");
        task.setStartTime(LocalDateTime.now());
        
        saveTask(task);
        return task;
    }
}
