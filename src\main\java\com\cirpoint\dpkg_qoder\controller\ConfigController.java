package com.cirpoint.dpkg_qoder.controller;

import com.cirpoint.dpkg_qoder.model.MigrateConfig;
import com.cirpoint.dpkg_qoder.model.OperationResult;
import com.cirpoint.dpkg_qoder.service.ConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 配置管理控制器
 * 提供配置管理相关的REST API
 * 
 * <AUTHOR> IDE
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/dpkg/config")
@CrossOrigin(origins = "*")
public class ConfigController {
    
    @Autowired
    private ConfigService configService;
    
    /**
     * 获取当前配置
     */
    @GetMapping
    public OperationResult<MigrateConfig> getConfig() {
        try {
            log.info("获取当前配置");
            MigrateConfig config = configService.getConfig();
            return OperationResult.success(config, "获取配置成功");
        } catch (Exception e) {
            log.error("获取配置失败", e);
            return OperationResult.failure("获取配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新配置
     */
    @PostMapping
    public OperationResult<MigrateConfig> updateConfig(@RequestBody MigrateConfig config) {
        try {
            log.info("更新配置: {}", config);
            return configService.updateConfig(config);
        } catch (Exception e) {
            log.error("更新配置失败", e);
            return OperationResult.failure("更新配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 重置为默认配置
     */
    @PostMapping("/reset")
    public OperationResult<MigrateConfig> resetToDefault() {
        try {
            log.info("重置配置为默认值");
            return configService.resetToDefault();
        } catch (Exception e) {
            log.error("重置配置失败", e);
            return OperationResult.failure("重置配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证配置
     */
    @PostMapping("/validate")
    public OperationResult<Void> validateConfig(@RequestBody MigrateConfig config) {
        try {
            log.info("验证配置: {}", config);
            // 这里可以加入更详细的验证逻辑，比如检查路径是否存在等
            return validatePaths(config);
        } catch (Exception e) {
            log.error("验证配置失败", e);
            return OperationResult.failure("验证配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证路径配置
     */
    private OperationResult<Void> validatePaths(MigrateConfig config) {
        StringBuilder errors = new StringBuilder();
        
        // 检查源目录
        java.io.File sourceDir = new java.io.File(config.getSourceRoot());
        if (!sourceDir.exists()) {
            errors.append("源目录不存在: ").append(config.getSourceRoot()).append("; ");
        } else if (!sourceDir.isDirectory()) {
            errors.append("源路径不是目录: ").append(config.getSourceRoot()).append("; ");
        }
        
        // 检查E盘目标目录的父目录
        java.io.File targetEParent = new java.io.File(config.getTargetE()).getParentFile();
        if (targetEParent != null && !targetEParent.exists()) {
            errors.append("E盘目标目录的父目录不存在: ").append(targetEParent.getAbsolutePath()).append("; ");
        }
        
        // 检查F盘目标目录的父目录
        java.io.File targetFParent = new java.io.File(config.getTargetF()).getParentFile();
        if (targetFParent != null && !targetFParent.exists()) {
            errors.append("F盘目标目录的父目录不存在: ").append(targetFParent.getAbsolutePath()).append("; ");
        }
        
        // 检查日志目录的父目录
        java.io.File logDirParent = new java.io.File(config.getLogDir()).getParentFile();
        if (logDirParent != null && !logDirParent.exists()) {
            errors.append("日志目录的父目录不存在: ").append(logDirParent.getAbsolutePath()).append("; ");
        }
        
        if (errors.length() > 0) {
            return OperationResult.failure("路径验证失败: " + errors.toString());
        }
        
        return OperationResult.success(null, "路径验证通过");
    }
}