package com.cirpoint.dpkg_qoder.model;

import lombok.Data;

/**
 * 数据迁移配置模型类
 * 对应批处理脚本01-config.bat中的配置项
 * 
 * <AUTHOR> IDE
 * @since 1.0.0
 */
@Data
public class MigrateConfig {
    
    /**
     * 源数据根目录
     */
    private String sourceRoot = "D:\\DataPkgFile";
    
    /**
     * E盘目标目录
     */
    private String targetE = "E:\\DataPkgFile";
    
    /**
     * F盘目标目录
     */
    private String targetF = "F:\\DataPkgFile";
    
    /**
     * 日志目录
     */
    private String logDir = "D:\\eclipse-work\\DataPkg-Migrate\\logs";
    
    /**
     * 服务名称（可选）
     * 用于在迁移过程中停止和启动服务
     */
    private String serviceName = "";
    
    /**
     * 分配策略
     * FILL_E_THEN_F: 优先填满E盘后使用F盘
     * BALANCE_BY_MONTH: 奇偶月均衡分配
     */
    private AllocationStrategy strategy = AllocationStrategy.FILL_E_THEN_F;
    
    /**
     * E盘最小预留空间（GB）
     */
    private Integer targetEMinFreeGb = 50;
    
    /**
     * F盘最小预留空间（GB）
     */
    private Integer targetFMinFreeGb = 50;
    
    /**
     * 预同步时排除最近N个月
     */
    private Integer excludeRecentMonths = 2;
    
    /**
     * Robocopy线程数
     */
    private Integer threads = 16;
    
    /**
     * Robocopy重试次数
     */
    private Integer roboRetry = 1;
    
    /**
     * Robocopy重试等待时间（秒）
     */
    private Integer roboWait = 2;
    
    /**
     * 干运行模式开关
     * true: 只模拟执行，不实际操作
     * false: 实际执行
     */
    private Boolean dryRun = true;
    
    /**
     * 切换后是否删除旧目录
     * true: 删除_old目录
     * false: 保留_old目录用于快速回滚
     */
    private Boolean deleteOld = false;
    
    /**
     * 抽样验证文件数量
     * 每个月随机抽取N个文件进行哈希校验
     * 0表示跳过抽样验证
     */
    private Integer verifySampleCount = 5;
    
    /**
     * 分配策略枚举
     */
    public enum AllocationStrategy {
        /**
         * 优先填满E盘后使用F盘
         */
        FILL_E_THEN_F,
        
        /**
         * 奇偶月均衡分配
         */
        BALANCE_BY_MONTH
    }
}