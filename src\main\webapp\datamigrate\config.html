<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>系统配置</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../layui/css/layui.css" media="all">
    <style>
        .config-form {
            padding: 20px;
        }
        .config-form .layui-form-item {
            margin-bottom: 20px;
        }
        .config-section {
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .config-section-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e6e6e6;
        }
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-ok { background-color: #4CAF50; }
        .status-error { background-color: #F44336; }
        .status-warning { background-color: #FF9800; }
        .help-text {
            color: #999;
            font-size: 12px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="config-form">
        <form class="layui-form" lay-filter="configForm">
            
            <!-- 基本配置 -->
            <div class="config-section">
                <div class="config-section-title">基本配置</div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">源目录</label>
                    <div class="layui-input-block">
                        <input type="text" name="sourceRoot" placeholder="请输入源目录路径" 
                               autocomplete="off" class="layui-input" lay-verify="required">
                        <div class="help-text">数据源目录，包含按月份命名的子目录（如：D:\DataPkgFile）</div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">E盘目标目录</label>
                    <div class="layui-input-block">
                        <input type="text" name="targetE" placeholder="请输入E盘目标目录路径" 
                               autocomplete="off" class="layui-input">
                        <div class="help-text">E盘目标目录（如：E:\DataPkgFile）</div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">F盘目标目录</label>
                    <div class="layui-input-block">
                        <input type="text" name="targetF" placeholder="请输入F盘目标目录路径" 
                               autocomplete="off" class="layui-input">
                        <div class="help-text">F盘目标目录（如：F:\DataPkgFile）</div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">日志目录</label>
                    <div class="layui-input-block">
                        <input type="text" name="logDir" placeholder="请输入日志目录路径" 
                               autocomplete="off" class="layui-input" lay-verify="required">
                        <div class="help-text">日志文件存储目录</div>
                    </div>
                </div>
            </div>
            
            <!-- 迁移策略 -->
            <div class="config-section">
                <div class="config-section-title">迁移策略</div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">分配策略</label>
                    <div class="layui-input-block">
                        <select name="strategy" lay-verify="required">
                            <option value="">请选择分配策略</option>
                            <option value="FILL_E_THEN_F">优先填满E盘</option>
                            <option value="BALANCE_BY_MONTH">奇偶月均衡分配</option>
                        </select>
                        <div class="help-text">
                            优先填满E盘：优先使用E盘，空间不足时使用F盘<br>
                            奇偶月均衡分配：奇数月优先E盘，偶数月优先F盘
                        </div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">E盘预留空间</label>
                    <div class="layui-input-block">
                        <input type="number" name="targetEMinFreeGb" placeholder="50" 
                               autocomplete="off" class="layui-input" lay-verify="required|number">
                        <div class="help-text">E盘最小预留空间（GB），低于此值停止向E盘写入</div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">F盘预留空间</label>
                    <div class="layui-input-block">
                        <input type="number" name="targetFMinFreeGb" placeholder="50" 
                               autocomplete="off" class="layui-input" lay-verify="required|number">
                        <div class="help-text">F盘最小预留空间（GB），低于此值停止向F盘写入</div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">排除最近月份</label>
                    <div class="layui-input-block">
                        <input type="number" name="excludeRecentMonths" placeholder="2" 
                               autocomplete="off" class="layui-input" lay-verify="required|number">
                        <div class="help-text">预同步时排除最近N个月（避免迁移正在使用的数据）</div>
                    </div>
                </div>
            </div>
            
            <!-- 执行参数 -->
            <div class="config-section">
                <div class="config-section-title">执行参数</div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">线程数</label>
                    <div class="layui-input-block">
                        <input type="number" name="threads" placeholder="16" 
                               autocomplete="off" class="layui-input" lay-verify="required|number">
                        <div class="help-text">Robocopy多线程数量（1-64）</div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">重试次数</label>
                    <div class="layui-input-block">
                        <input type="number" name="roboRetry" placeholder="1" 
                               autocomplete="off" class="layui-input" lay-verify="required|number">
                        <div class="help-text">Robocopy失败重试次数</div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">等待时间</label>
                    <div class="layui-input-block">
                        <input type="number" name="roboWait" placeholder="2" 
                               autocomplete="off" class="layui-input" lay-verify="required|number">
                        <div class="help-text">Robocopy重试等待时间（秒）</div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">抽样验证数量</label>
                    <div class="layui-input-block">
                        <input type="number" name="verifySampleCount" placeholder="5" 
                               autocomplete="off" class="layui-input" lay-verify="number">
                        <div class="help-text">每月抽样验证文件数量，0表示跳过验证</div>
                    </div>
                </div>
            </div>
            
            <!-- 安全选项 -->
            <div class="config-section">
                <div class="config-section-title">安全选项</div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">服务名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="serviceName" placeholder="可选，如：Tomcat8.5" 
                               autocomplete="off" class="layui-input">
                        <div class="help-text">迁移时需要停止的服务名称（可选）</div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <input type="checkbox" name="dryRun" title="演练模式" lay-skin="primary">
                        <div class="help-text">开启后只打印命令不执行，用于测试</div>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <input type="checkbox" name="deleteOld" title="删除备份目录" lay-skin="primary">
                        <div class="help-text">切换完成后自动删除_old备份目录</div>
                    </div>
                </div>
            </div>
            
            <!-- 状态检查 -->
            <div class="config-section">
                <div class="config-section-title">状态检查</div>
                <div id="statusCheck">
                    <p><span class="status-indicator status-warning"></span>检查中...</p>
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button class="layui-btn" lay-submit lay-filter="saveConfig">保存配置</button>
                    <button type="button" class="layui-btn layui-btn-primary" id="validateBtn">验证配置</button>
                    <button type="button" class="layui-btn layui-btn-danger" id="resetBtn">重置默认</button>
                    <button type="button" class="layui-btn layui-btn-primary" id="cancelBtn">取消</button>
                </div>
            </div>
        </form>
    </div>

    <script src="../layui/layui.js"></script>
    <script>
        layui.use(['form', 'layer', 'jquery'], function(){
            var form = layui.form;
            var layer = layui.layer;
            var $ = layui.$;
            
            // 加载当前配置
            loadCurrentConfig();
            
            // 表单提交
            form.on('submit(saveConfig)', function(data) {
                var formData = data.field;
                
                // 转换数据类型
                formData.targetEMinFreeGb = parseInt(formData.targetEMinFreeGb);
                formData.targetFMinFreeGb = parseInt(formData.targetFMinFreeGb);
                formData.excludeRecentMonths = parseInt(formData.excludeRecentMonths);
                formData.threads = parseInt(formData.threads);
                formData.roboRetry = parseInt(formData.roboRetry);
                formData.roboWait = parseInt(formData.roboWait);
                formData.verifySampleCount = parseInt(formData.verifySampleCount) || 0;
                formData.dryRun = formData.dryRun === 'on';
                formData.deleteOld = formData.deleteOld === 'on';
                
                $.ajax({
                    url: '/api/config',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                    success: function(res) {
                        if (res.success) {
                            layer.msg('配置保存成功');
                            checkConfigStatus();
                        } else {
                            layer.msg('保存失败：' + res.message);
                        }
                    },
                    error: function() {
                        layer.msg('保存失败：网络错误');
                    }
                });
                
                return false;
            });
            
            // 验证配置
            $('#validateBtn').on('click', function() {
                var formData = form.val('configForm');
                
                // 转换数据类型
                formData.targetEMinFreeGb = parseInt(formData.targetEMinFreeGb);
                formData.targetFMinFreeGb = parseInt(formData.targetFMinFreeGb);
                formData.excludeRecentMonths = parseInt(formData.excludeRecentMonths);
                formData.threads = parseInt(formData.threads);
                formData.roboRetry = parseInt(formData.roboRetry);
                formData.roboWait = parseInt(formData.roboWait);
                formData.verifySampleCount = parseInt(formData.verifySampleCount) || 0;
                formData.dryRun = formData.dryRun === 'on';
                formData.deleteOld = formData.deleteOld === 'on';
                
                $.ajax({
                    url: '/api/config/validate',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                    success: function(res) {
                        if (res.success) {
                            layer.msg('配置验证通过', {icon: 1});
                        } else {
                            layer.msg('验证失败：' + res.message, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.msg('验证失败：网络错误', {icon: 2});
                    }
                });
            });
            
            // 重置配置
            $('#resetBtn').on('click', function() {
                layer.confirm('确定要重置为默认配置吗？', function(index) {
                    $.post('/api/config/reset', {}, function(res) {
                        if (res.success) {
                            layer.msg('重置成功');
                            loadCurrentConfig();
                        } else {
                            layer.msg('重置失败：' + res.message);
                        }
                    });
                    layer.close(index);
                });
            });
            
            // 取消
            $('#cancelBtn').on('click', function() {
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            });
            
            // 加载当前配置
            function loadCurrentConfig() {
                $.get('/api/config', function(res) {
                    if (res.success) {
                        form.val('configForm', res.data);
                        checkConfigStatus();
                    } else {
                        layer.msg('加载配置失败：' + res.message);
                    }
                });
            }
            
            // 检查配置状态
            function checkConfigStatus() {
                $.get('/api/config/status', function(res) {
                    if (res.success) {
                        var html = '';
                        var data = res.data;
                        
                        // 源目录状态
                        if (data.sourceExists) {
                            html += '<p><span class="status-indicator status-ok"></span>源目录：正常</p>';
                        } else {
                            html += '<p><span class="status-indicator status-error"></span>源目录：不存在</p>';
                        }
                        
                        // E盘状态
                        if (data.targetEExists) {
                            var spaceStatus = data.targetESpaceSufficient ? '充足' : '不足';
                            var spaceClass = data.targetESpaceSufficient ? 'status-ok' : 'status-warning';
                            html += '<p><span class="status-indicator ' + spaceClass + '"></span>E盘：正常，剩余空间' + spaceStatus + '（' + (data.targetEFreeSpaceGB || 0) + 'GB）</p>';
                        } else {
                            html += '<p><span class="status-indicator status-error"></span>E盘：目录不存在</p>';
                        }
                        
                        // F盘状态
                        if (data.targetFExists) {
                            var spaceStatus = data.targetFSpaceSufficient ? '充足' : '不足';
                            var spaceClass = data.targetFSpaceSufficient ? 'status-ok' : 'status-warning';
                            html += '<p><span class="status-indicator ' + spaceClass + '"></span>F盘：正常，剩余空间' + spaceStatus + '（' + (data.targetFFreeSpaceGB || 0) + 'GB）</p>';
                        } else {
                            html += '<p><span class="status-indicator status-error"></span>F盘：目录不存在</p>';
                        }
                        
                        // 日志目录状态
                        if (data.logDirExists) {
                            html += '<p><span class="status-indicator status-ok"></span>日志目录：正常</p>';
                        } else {
                            html += '<p><span class="status-indicator status-warning"></span>日志目录：不存在（将自动创建）</p>';
                        }
                        
                        // 服务状态
                        if (data.serviceConfigured) {
                            html += '<p><span class="status-indicator status-ok"></span>服务：已配置</p>';
                        } else {
                            html += '<p><span class="status-indicator status-warning"></span>服务：未配置</p>';
                        }
                        
                        $('#statusCheck').html(html);
                    }
                });
            }
        });
    </script>
</body>
</html>
