package com.cirpoint.dpkg_qoder.service;

import com.cirpoint.dpkg_qoder.model.*;
import com.cirpoint.dpkg_qoder.util.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 月份管理服务
 * 实现10-list-months.bat的功能
 * 
 * <AUTHOR> IDE
 * @since 1.0.0
 */
@Slf4j
@Service
public class MonthListService {
    
    @Autowired
    private ConfigService configService;
    
    /**
     * 获取所有月份目录
     */
    public OperationResult<List<MonthInfo>> getAllMonths() {
        try {
            MigrateConfig config = configService.getConfig();
            String sourceRoot = config.getSourceRoot();
            
            log.info("扫描源目录获取月份列表: {}", sourceRoot);
            
            List<String> monthNames = FileUtils.getMonthDirectories(sourceRoot);
            List<MonthInfo> monthInfos = new ArrayList<>();
            
            for (String monthName : monthNames) {
                MonthInfo monthInfo = createMonthInfo(monthName, config);
                monthInfos.add(monthInfo);
            }
            
            log.info("找到 {} 个月份目录", monthInfos.size());
            return OperationResult.success(monthInfos, "获取月份列表成功");
            
        } catch (Exception e) {
            log.error("获取月份列表失败", e);
            return OperationResult.failure("获取月份列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取可迁移的月份（排除最近N个月）
     */
    public OperationResult<List<MonthInfo>> getMigratableMonths() {
        try {
            MigrateConfig config = configService.getConfig();
            String sourceRoot = config.getSourceRoot();
            int excludeRecentMonths = config.getExcludeRecentMonths();
            
            log.info("获取可迁移月份列表，排除最近 {} 个月", excludeRecentMonths);
            
            List<String> allMonths = FileUtils.getMonthDirectories(sourceRoot);
            List<String> migratableMonths = FileUtils.filterRecentMonths(allMonths, excludeRecentMonths);
            
            List<MonthInfo> monthInfos = new ArrayList<>();
            
            for (String monthName : migratableMonths) {
                MonthInfo monthInfo = createMonthInfo(monthName, config);
                
                // 只包含可以迁移的月份
                if (monthInfo.getCanMigrate()) {
                    monthInfos.add(monthInfo);
                }
            }
            
            log.info("找到 {} 个可迁移月份", monthInfos.size());
            return OperationResult.success(monthInfos, "获取可迁移月份成功");
            
        } catch (Exception e) {
            log.error("获取可迁移月份失败", e);
            return OperationResult.failure("获取可迁移月份失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取指定月份信息
     */
    public OperationResult<MonthInfo> getMonthInfo(String monthName) {
        try {
            MigrateConfig config = configService.getConfig();
            MonthInfo monthInfo = createMonthInfo(monthName, config);
            
            return OperationResult.success(monthInfo, "获取月份信息成功");
            
        } catch (Exception e) {
            log.error("获取月份信息失败: {}", monthName, e);
            return OperationResult.failure("获取月份信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取磁盘空间信息
     */
    public OperationResult<List<DiskSpaceInfo>> getDiskSpaceInfo() {
        try {
            MigrateConfig config = configService.getConfig();
            List<DiskSpaceInfo> diskInfos = new ArrayList<>();
            
            // E盘信息
            DiskSpaceInfo eDiskInfo = createDiskSpaceInfo("E:", config.getTargetE(), config.getTargetEMinFreeGb());
            diskInfos.add(eDiskInfo);
            
            // F盘信息
            DiskSpaceInfo fDiskInfo = createDiskSpaceInfo("F:", config.getTargetF(), config.getTargetFMinFreeGb());
            diskInfos.add(fDiskInfo);
            
            return OperationResult.success(diskInfos, "获取磁盘空间信息成功");
            
        } catch (Exception e) {
            log.error("获取磁盘空间信息失败", e);
            return OperationResult.failure("获取磁盘空间信息失败: " + e.getMessage());
        }
    }
    
    /**
     * 为月份选择推荐的目标驱动器
     */
    public OperationResult<String> selectTargetDrive(String monthName) {
        try {
            MigrateConfig config = configService.getConfig();
            String targetDrive = selectTargetForMonth(monthName, config);
            
            if (targetDrive != null) {
                return OperationResult.success(targetDrive, "选择目标驱动器成功");
            } else {
                return OperationResult.failure("没有可用的目标驱动器");
            }
            
        } catch (Exception e) {
            log.error("选择目标驱动器失败: {}", monthName, e);
            return OperationResult.failure("选择目标驱动器失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建月份信息对象
     */
    private MonthInfo createMonthInfo(String monthName, MigrateConfig config) {
        MonthInfo monthInfo = new MonthInfo(monthName);
        
        String sourcePath = Paths.get(config.getSourceRoot(), monthName).toString();
        monthInfo.setSourcePath(sourcePath);
        
        File sourceDir = new File(sourcePath);
        monthInfo.setExists(sourceDir.exists());
        
        if (monthInfo.getExists()) {
            // 检查是否为联接点
            boolean isJunction = FileUtils.isJunctionPoint(sourcePath);
            monthInfo.setIsJunction(isJunction);
            
            if (isJunction) {
                // 获取联接目标
                String junctionTarget = FileUtils.getJunctionTarget(sourcePath);
                monthInfo.setJunctionTarget(junctionTarget);
                monthInfo.setStatus(MonthInfo.MonthStatus.MIGRATED);
                monthInfo.setCanMigrate(false);
                monthInfo.setCannotMigrateReason("已经迁移（联接点）");
            } else {
                // 普通目录
                monthInfo.setStatus(MonthInfo.MonthStatus.NORMAL);
                monthInfo.setCanMigrate(true);
                
                // 获取目录大小
                long size = FileUtils.getDirectorySize(sourcePath);
                monthInfo.setDirectorySize(size);
                monthInfo.setFormattedSize(FileUtils.formatFileSize(size));
                
                // 选择推荐的目标驱动器
                String recommendedTarget = selectTargetForMonth(monthName, config);
                monthInfo.setRecommendedTarget(recommendedTarget);
                
                if (recommendedTarget != null) {
                    if (recommendedTarget.equals("E:")) {
                        monthInfo.setTargetPath(Paths.get(config.getTargetE(), monthName).toString());
                    } else {
                        monthInfo.setTargetPath(Paths.get(config.getTargetF(), monthName).toString());
                    }
                } else {
                    monthInfo.setCanMigrate(false);
                    monthInfo.setCannotMigrateReason("没有可用的目标驱动器");
                }
            }
        } else {
            monthInfo.setStatus(MonthInfo.MonthStatus.NOT_EXISTS);
            monthInfo.setCanMigrate(false);
            monthInfo.setCannotMigrateReason("源目录不存在");
        }
        
        return monthInfo;
    }
    
    /**
     * 为指定月份选择目标驱动器
     */
    private String selectTargetForMonth(String monthName, MigrateConfig config) {
        long eFreeSpace = FileUtils.getDiskFreeSpaceGB("E:");
        long fFreeSpace = FileUtils.getDiskFreeSpaceGB("F:");
        
        if (config.getStrategy() == MigrateConfig.AllocationStrategy.BALANCE_BY_MONTH) {
            // 奇偶月均衡分配
            String[] parts = monthName.split("-");
            if (parts.length == 2) {
                try {
                    int month = Integer.parseInt(parts[1]);
                    if (month % 2 == 1) {
                        // 奇数月优先E盘
                        if (eFreeSpace > config.getTargetEMinFreeGb()) {
                            return "E:";
                        } else if (fFreeSpace > config.getTargetFMinFreeGb()) {
                            return "F:";
                        }
                    } else {
                        // 偶数月优先F盘
                        if (fFreeSpace > config.getTargetFMinFreeGb()) {
                            return "F:";
                        } else if (eFreeSpace > config.getTargetEMinFreeGb()) {
                            return "E:";
                        }
                    }
                } catch (NumberFormatException e) {
                    log.warn("解析月份失败: {}", monthName);
                }
            }
        } else {
            // 优先填满E盘
            if (eFreeSpace > config.getTargetEMinFreeGb()) {
                return "E:";
            } else if (fFreeSpace > config.getTargetFMinFreeGb()) {
                return "F:";
            }
        }
        
        return null; // 没有可用的驱动器
    }
    
    /**
     * 创建磁盘空间信息
     */
    private DiskSpaceInfo createDiskSpaceInfo(String drive, String targetPath, int minFreeGb) {
        DiskSpaceInfo diskInfo = new DiskSpaceInfo();
        diskInfo.setDrive(drive);
        
        long totalSpace = FileUtils.getDiskTotalSpaceGB(drive);
        long freeSpace = FileUtils.getDiskFreeSpaceGB(drive);
        long usedSpace = totalSpace - freeSpace;
        
        diskInfo.setTotalSpaceGb(totalSpace);
        diskInfo.setFreeSpaceGb(freeSpace);
        diskInfo.setUsedSpaceGb(usedSpace);
        
        if (totalSpace > 0) {
            double usagePercentage = (double) usedSpace / totalSpace * 100;
            diskInfo.setUsagePercentage(usagePercentage);
        } else {
            diskInfo.setUsagePercentage(0.0);
        }
        
        diskInfo.setHasEnoughSpace(freeSpace > minFreeGb);
        
        return diskInfo;
    }
}