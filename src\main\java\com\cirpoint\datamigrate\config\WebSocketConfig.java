package com.cirpoint.datamigrate.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

/**
 * WebSocket配置类
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
@Configuration
public class WebSocketConfig {

    /**
     * 注入ServerEndpointExporter，这个bean会自动注册使用了@ServerEndpoint注解声明的WebSocket endpoint
     * 注意：如果使用外部Tomcat部署，不需要此Bean
     */
    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }
}
