package com.cirpoint.datamigrate.config;

import org.springframework.boot.web.servlet.ServletContextInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import javax.websocket.server.ServerContainer;
import javax.websocket.server.ServerEndpointConfig;

/**
 * WebSocket配置类
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Configuration
public class WebSocketConfig {

    /**
     * 配置WebSocket端点
     */
    @Bean
    public ServletContextInitializer webSocketServletContextInitializer() {
        return new ServletContextInitializer() {
            @Override
            public void onStartup(ServletContext servletContext) throws ServletException {
                try {
                    ServerContainer serverContainer = (ServerContainer) servletContext.getAttribute(ServerContainer.class.getName());
                    if (serverContainer != null) {
                        serverContainer.addEndpoint(com.cirpoint.datamigrate.websocket.TaskProgressWebSocket.class);
                    }
                } catch (Exception e) {
                    throw new ServletException("Failed to register WebSocket endpoint", e);
                }
            }
        };
    }
}
