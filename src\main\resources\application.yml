# 数据迁移系统配置
server:
  port: 8080
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
    max-threads: 200
    min-spare-threads: 10

spring:
  application:
    name: data-migrate-system
  
  # 静态资源配置
  web:
    resources:
      static-locations: classpath:/META-INF/resources/
      cache-period: 3600
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
  
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# 数据迁移配置
migrate:
  # 源与目标目录
  source-root: D:\DataPkgFile
  target-e: E:\DataPkgFile
  target-f: F:\DataPkgFile
  
  # 日志目录
  log-dir: D:\eclipse-work\DataPkg-Migrate\logs
  
  # 服务名称（可选）
  service-name: ""
  
  # 分配策略：FILL_E_THEN_F 或 BALANCE_BY_MONTH
  strategy: FILL_E_THEN_F
  
  # 预留容量（GB）
  target-e-min-free-gb: 50
  target-f-min-free-gb: 50
  
  # 预同步排除最近N个月
  exclude-recent-months: 2
  
  # 多线程与重试
  threads: 16
  robo-retry: 1
  robo-wait: 2
  
  # 安全开关：true=演练模式，false=执行模式
  dry-run: true
  
  # 切换后是否删除_old目录
  delete-old: false
  
  # 抽样验证文件数量
  verify-sample-count: 5

# 日志配置
logging:
  level:
    root: INFO
    com.cirpoint.datamigrate: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/data-migrate-system.log
    max-size: 100MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true
