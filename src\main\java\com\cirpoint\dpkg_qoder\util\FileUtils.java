package com.cirpoint.dpkg_qoder.util;

import com.cirpoint.dpkg_qoder.model.MigrateConstants;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 文件操作工具类
 * 提供文件系统相关的操作功能
 * 
 * <AUTHOR> IDE
 * @since 1.0.0
 */
@Slf4j
public class FileUtils {
    
    /**
     * 月份格式模式
     */
    private static final Pattern MONTH_PATTERN = Pattern.compile(MigrateConstants.MONTH_PATTERN);
    
    /**
     * 日期时间格式化器
     */
    private static final DateTimeFormatter DATETIME_FORMATTER = 
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 文件大小格式化器
     */
    private static final DecimalFormat SIZE_FORMAT = new DecimalFormat("#,##0.00");
    
    /**
     * 获取指定目录下的所有月份目录
     */
    public static List<String> getMonthDirectories(String sourceRoot) {
        List<String> months = new ArrayList<>();
        
        try {
            Path sourcePath = Paths.get(sourceRoot);
            if (!Files.exists(sourcePath) || !Files.isDirectory(sourcePath)) {
                log.warn("源目录不存在或不是目录: {}", sourceRoot);
                return months;
            }
            
            try (Stream<Path> paths = Files.list(sourcePath)) {
                months = paths
                        .filter(Files::isDirectory)
                        .map(path -> path.getFileName().toString())
                        .filter(name -> MONTH_PATTERN.matcher(name).matches())
                        .sorted()
                        .collect(Collectors.toList());
            }
            
            log.info("在目录 {} 中找到 {} 个月份目录", sourceRoot, months.size());
            
        } catch (IOException e) {
            log.error("获取月份目录失败: {}", sourceRoot, e);
        }
        
        return months;
    }
    
    /**
     * 过滤月份列表，排除最近N个月
     */
    public static List<String> filterRecentMonths(List<String> months, int excludeRecentMonths) {
        if (months.isEmpty() || excludeRecentMonths <= 0) {
            return new ArrayList<>(months);
        }
        
        List<String> sorted = new ArrayList<>(months);
        sorted.sort(String::compareTo);
        
        int toIndex = Math.max(0, sorted.size() - excludeRecentMonths);
        List<String> filtered = sorted.subList(0, toIndex);
        
        log.info("原始月份数量: {}, 排除最近 {} 个月后剩余: {}", 
                months.size(), excludeRecentMonths, filtered.size());
        
        return filtered;
    }
    
    /**
     * 获取磁盘可用空间（GB）
     */
    public static long getDiskFreeSpaceGB(String drive) {
        try {
            Path path = Paths.get(drive);
            FileStore store = Files.getFileStore(path);
            long freeBytes = store.getUsableSpace();
            return freeBytes / MigrateConstants.GB_SIZE;
        } catch (IOException e) {
            log.error("获取磁盘 {} 可用空间失败", drive, e);
            return 0;
        }
    }
    
    /**
     * 获取磁盘总空间（GB）
     */
    public static long getDiskTotalSpaceGB(String drive) {
        try {
            Path path = Paths.get(drive);
            FileStore store = Files.getFileStore(path);
            long totalBytes = store.getTotalSpace();
            return totalBytes / MigrateConstants.GB_SIZE;
        } catch (IOException e) {
            log.error("获取磁盘 {} 总空间失败", drive, e);
            return 0;
        }
    }
    
    /**
     * 获取目录大小（字节）
     */
    public static long getDirectorySize(String directoryPath) {
        try {
            Path path = Paths.get(directoryPath);
            if (!Files.exists(path) || !Files.isDirectory(path)) {
                return 0;
            }
            
            try (Stream<Path> paths = Files.walk(path)) {
                return paths
                        .filter(Files::isRegularFile)
                        .mapToLong(p -> {
                            try {
                                return Files.size(p);
                            } catch (IOException e) {
                                log.warn("获取文件大小失败: {}", p, e);
                                return 0;
                            }
                        })
                        .sum();
            }
            
        } catch (IOException e) {
            log.error("获取目录大小失败: {}", directoryPath, e);
            return 0;
        }
    }
    
    /**
     * 创建目录（包括父目录）
     */
    public static boolean createDirectories(String directoryPath) {
        try {
            Path path = Paths.get(directoryPath);
            Files.createDirectories(path);
            log.info("创建目录成功: {}", directoryPath);
            return true;
        } catch (IOException e) {
            log.error("创建目录失败: {}", directoryPath, e);
            return false;
        }
    }
    
    /**
     * 检查目录是否为联接点
     */
    public static boolean isJunctionPoint(String directoryPath) {
        try {
            Path path = Paths.get(directoryPath);
            return Files.exists(path) && Files.isSymbolicLink(path);
        } catch (Exception e) {
            log.error("检查联接点失败: {}", directoryPath, e);
            return false;
        }
    }
    
    /**
     * 获取联接点目标
     */
    public static String getJunctionTarget(String junctionPath) {
        try {
            Path path = Paths.get(junctionPath);
            if (Files.isSymbolicLink(path)) {
                Path target = Files.readSymbolicLink(path);
                return target.toAbsolutePath().toString();
            }
        } catch (IOException e) {
            log.error("获取联接点目标失败: {}", junctionPath, e);
        }
        return null;
    }
    
    /**
     * 写入日志文件
     */
    public static void writeLogFile(String logFilePath, List<String> logLines) {
        try {
            Path logPath = Paths.get(logFilePath);
            
            // 创建日志目录
            Path logDir = logPath.getParent();
            if (logDir != null && !Files.exists(logDir)) {
                Files.createDirectories(logDir);
            }
            
            // 写入日志
            try (BufferedWriter writer = Files.newBufferedWriter(logPath, 
                    StandardCharsets.UTF_8, StandardOpenOption.CREATE, StandardOpenOption.APPEND)) {
                
                writer.write("=== " + LocalDateTime.now().format(DATETIME_FORMATTER) + " ===");
                writer.newLine();
                
                for (String line : logLines) {
                    writer.write(line);
                    writer.newLine();
                }
                
                writer.newLine();
            }
            
        } catch (IOException e) {
            log.error("写入日志文件失败: {}", logFilePath, e);
        }
    }
    
    /**
     * 读取日志文件
     */
    public static List<String> readLogFile(String logFilePath) {
        try {
            Path logPath = Paths.get(logFilePath);
            if (!Files.exists(logPath)) {
                return new ArrayList<>();
            }
            
            return Files.readAllLines(logPath, StandardCharsets.UTF_8);
            
        } catch (IOException e) {
            log.error("读取日志文件失败: {}", logFilePath, e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 获取日志文件路径
     */
    public static String getLogFilePath(String logDir, String prefix, String month) {
        return Paths.get(logDir, prefix + month + MigrateConstants.LOG_FILE_EXTENSION).toString();
    }
    
    /**
     * 格式化文件大小
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return SIZE_FORMAT.format(bytes / 1024.0) + " KB";
        } else if (bytes < 1024 * 1024 * 1024) {
            return SIZE_FORMAT.format(bytes / (1024.0 * 1024.0)) + " MB";
        } else {
            return SIZE_FORMAT.format(bytes / (1024.0 * 1024.0 * 1024.0)) + " GB";
        }
    }
    
    /**
     * 获取文件扩展名
     */
    public static String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == fileName.length() - 1) {
            return "";
        }
        return fileName.substring(lastDotIndex + 1);
    }
    
    /**
     * 获取随机文件列表（用于抽样验证）
     */
    public static List<String> getRandomFiles(String directoryPath, int count) {
        List<String> files = new ArrayList<>();
        
        try {
            Path path = Paths.get(directoryPath);
            if (!Files.exists(path) || !Files.isDirectory(path)) {
                return files;
            }
            
            try (Stream<Path> paths = Files.walk(path)) {
                List<String> allFiles = paths
                        .filter(Files::isRegularFile)
                        .map(p -> p.toAbsolutePath().toString())
                        .collect(Collectors.toList());
                
                Collections.shuffle(allFiles);
                
                int resultCount = Math.min(count, allFiles.size());
                files = allFiles.subList(0, resultCount);
            }
            
        } catch (IOException e) {
            log.error("获取随机文件列表失败: {}", directoryPath, e);
        }
        
        return files;
    }
    
    /**
     * 安全删除目录
     */
    public static boolean safeDeleteDirectory(String directoryPath) {
        try {
            Path path = Paths.get(directoryPath);
            if (!Files.exists(path)) {
                return true;
            }
            
            if (Files.isSymbolicLink(path)) {
                // 如果是联接点，直接删除联接
                Files.delete(path);
                log.info("删除联接点成功: {}", directoryPath);
                return true;
            } else {
                // 如果是普通目录，递归删除
                try (Stream<Path> paths = Files.walk(path)) {
                    paths.sorted(Comparator.reverseOrder())
                         .forEach(p -> {
                             try {
                                 Files.delete(p);
                             } catch (IOException e) {
                                 log.warn("删除文件失败: {}", p, e);
                             }
                         });
                }
                log.info("删除目录成功: {}", directoryPath);
                return true;
            }
            
        } catch (IOException e) {
            log.error("删除目录失败: {}", directoryPath, e);
            return false;
        }
    }
}