package com.cirpoint.datamigrate.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * 系统命令执行工具类
 * 用于执行Windows系统命令，如robocopy、mklink、wmic等
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
public class SystemCommandUtil {
    
    /**
     * 默认命令超时时间（分钟）
     */
    private static final int DEFAULT_TIMEOUT_MINUTES = 60;
    
    /**
     * Windows命令行编码
     */
    private static final String WINDOWS_CHARSET = "GBK";
    
    /**
     * 执行系统命令
     * 
     * @param command 命令字符串
     * @return 命令执行结果
     */
    public static CommandResult executeCommand(String command) {
        return executeCommand(command, null, DEFAULT_TIMEOUT_MINUTES, null);
    }
    
    /**
     * 执行系统命令
     * 
     * @param command 命令字符串
     * @param workDir 工作目录
     * @return 命令执行结果
     */
    public static CommandResult executeCommand(String command, String workDir) {
        return executeCommand(command, workDir, DEFAULT_TIMEOUT_MINUTES, null);
    }
    
    /**
     * 执行系统命令
     * 
     * @param command 命令字符串
     * @param workDir 工作目录
     * @param timeoutMinutes 超时时间（分钟）
     * @param outputConsumer 输出消费者（实时处理输出）
     * @return 命令执行结果
     */
    public static CommandResult executeCommand(String command, String workDir, 
                                             int timeoutMinutes, Consumer<String> outputConsumer) {
        log.info("执行命令: {}", command);
        if (StrUtil.isNotBlank(workDir)) {
            log.info("工作目录: {}", workDir);
        }
        
        CommandResult result = new CommandResult();
        result.setCommand(command);
        result.setStartTime(System.currentTimeMillis());
        
        try {
            // 构建命令
            List<String> cmdList = new ArrayList<>();
            cmdList.add("cmd");
            cmdList.add("/c");
            cmdList.add(command);
            
            ProcessBuilder pb = new ProcessBuilder(cmdList);
            
            // 设置工作目录
            if (StrUtil.isNotBlank(workDir)) {
                File workDirFile = new File(workDir);
                if (workDirFile.exists() && workDirFile.isDirectory()) {
                    pb.directory(workDirFile);
                }
            }
            
            // 合并错误输出到标准输出
            pb.redirectErrorStream(true);
            
            Process process = pb.start();
            
            // 读取输出
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getInputStream(), WINDOWS_CHARSET))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                    if (outputConsumer != null) {
                        outputConsumer.accept(line);
                    }
                }
            }
            
            // 等待命令执行完成
            boolean finished = process.waitFor(timeoutMinutes, TimeUnit.MINUTES);
            
            result.setEndTime(System.currentTimeMillis());
            result.setOutput(output.toString());
            
            if (!finished) {
                // 超时，强制终止进程
                process.destroyForcibly();
                result.setExitCode(-1);
                result.setSuccess(false);
                result.setErrorMessage("命令执行超时（" + timeoutMinutes + "分钟）");
                log.error("命令执行超时: {}", command);
            } else {
                int exitCode = process.exitValue();
                result.setExitCode(exitCode);
                result.setSuccess(exitCode == 0);
                
                if (exitCode != 0) {
                    result.setErrorMessage("命令执行失败，退出码: " + exitCode);
                    log.error("命令执行失败: {}, 退出码: {}", command, exitCode);
                } else {
                    log.info("命令执行成功: {}", command);
                }
            }
            
        } catch (Exception e) {
            result.setEndTime(System.currentTimeMillis());
            result.setSuccess(false);
            result.setErrorMessage("命令执行异常: " + e.getMessage());
            log.error("命令执行异常: " + command, e);
        }
        
        return result;
    }
    
    /**
     * 检查目录是否为NTFS联接
     * 
     * @param dirPath 目录路径
     * @return true表示是联接，false表示不是
     */
    public static boolean isJunction(String dirPath) {
        try {
            Path path = Paths.get(dirPath);
            if (!Files.exists(path) || !Files.isDirectory(path)) {
                return false;
            }
            
            // 使用dir命令检查是否为联接
            String command = "dir \"" + dirPath + "\"";
            CommandResult result = executeCommand(command);
            
            return result.isSuccess() && result.getOutput().contains("<JUNCTION>");
            
        } catch (Exception e) {
            log.error("检查联接状态失败: " + dirPath, e);
            return false;
        }
    }
    
    /**
     * 获取磁盘剩余空间（GB）
     * 
     * @param drive 磁盘驱动器，如"E:"
     * @return 剩余空间（GB），失败返回-1
     */
    public static long getDiskFreeSpaceGB(String drive) {
        try {
            String command = "wmic logicaldisk where \"DeviceID='" + drive + "'\" get FreeSpace /value";
            CommandResult result = executeCommand(command);
            
            if (!result.isSuccess()) {
                return -1;
            }
            
            String output = result.getOutput();
            String[] lines = output.split("\n");
            
            for (String line : lines) {
                if (line.contains("FreeSpace=")) {
                    String freeSpaceStr = line.substring(line.indexOf("=") + 1).trim();
                    if (StrUtil.isNotBlank(freeSpaceStr) && freeSpaceStr.matches("\\d+")) {
                        long freeBytes = Long.parseLong(freeSpaceStr);
                        return freeBytes / (1024 * 1024 * 1024); // 转换为GB
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("获取磁盘剩余空间失败: " + drive, e);
        }
        
        return -1;
    }
    
    /**
     * 命令执行结果类
     */
    public static class CommandResult {
        private String command;
        private boolean success;
        private int exitCode;
        private String output;
        private String errorMessage;
        private long startTime;
        private long endTime;
        
        // Getters and Setters
        public String getCommand() { return command; }
        public void setCommand(String command) { this.command = command; }
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public int getExitCode() { return exitCode; }
        public void setExitCode(int exitCode) { this.exitCode = exitCode; }
        
        public String getOutput() { return output; }
        public void setOutput(String output) { this.output = output; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        
        public long getExecutionTime() { return endTime - startTime; }
        
        @Override
        public String toString() {
            return "CommandResult{" +
                    "command='" + command + '\'' +
                    ", success=" + success +
                    ", exitCode=" + exitCode +
                    ", executionTime=" + getExecutionTime() + "ms" +
                    '}';
        }
    }
}
