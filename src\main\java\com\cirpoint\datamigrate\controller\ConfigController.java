package com.cirpoint.datamigrate.controller;

import com.cirpoint.datamigrate.config.MigrateConfig;
import com.cirpoint.datamigrate.service.ConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 配置管理控制器
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
@RestController
@RequestMapping("/api/config")
public class ConfigController {
    
    @Autowired
    private ConfigService configService;
    
    /**
     * 获取当前配置
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> getConfig() {
        try {
            MigrateConfig config = configService.getConfig();
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", config);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取配置失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取配置失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 更新配置
     */
    @PostMapping
    public ResponseEntity<Map<String, Object>> updateConfig(@RequestBody MigrateConfig newConfig) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            configService.updateConfig(newConfig);
            result.put("success", true);
            result.put("message", "配置更新成功");
            return ResponseEntity.ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("配置验证失败: {}", e.getMessage());
            result.put("success", false);
            result.put("message", "配置验证失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("更新配置失败", e);
            result.put("success", false);
            result.put("message", "更新配置失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 验证配置
     */
    @PostMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateConfig(@RequestBody MigrateConfig config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            configService.validateConfig(config);
            result.put("success", true);
            result.put("message", "配置验证通过");
            return ResponseEntity.ok(result);
        } catch (IllegalArgumentException e) {
            log.warn("配置验证失败: {}", e.getMessage());
            result.put("success", false);
            result.put("message", e.getMessage());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("配置验证异常", e);
            result.put("success", false);
            result.put("message", "配置验证异常: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 检查配置状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getConfigStatus() {
        try {
            Map<String, Object> status = configService.checkConfigStatus();
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", status);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("检查配置状态失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "检查配置状态失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取配置摘要
     */
    @GetMapping("/summary")
    public ResponseEntity<Map<String, Object>> getConfigSummary() {
        try {
            Map<String, Object> summary = configService.getConfigSummary();
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", summary);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取配置摘要失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取配置摘要失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 重置为默认配置
     */
    @PostMapping("/reset")
    public ResponseEntity<Map<String, Object>> resetConfig() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            configService.resetToDefault();
            result.put("success", true);
            result.put("message", "配置已重置为默认值");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("重置配置失败", e);
            result.put("success", false);
            result.put("message", "重置配置失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
    
    /**
     * 获取分配策略选项
     */
    @GetMapping("/strategies")
    public ResponseEntity<Map<String, Object>> getStrategies() {
        try {
            Map<String, Object> strategies = new HashMap<>();
            for (MigrateConfig.Strategy strategy : MigrateConfig.Strategy.values()) {
                strategies.put(strategy.getCode(), strategy.getDescription());
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("data", strategies);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取策略选项失败", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "获取策略选项失败: " + e.getMessage());
            return ResponseEntity.ok(result);
        }
    }
}
