package com.cirpoint.datamigrate;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 数据迁移系统主应用程序
 * 
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
@SpringBootApplication
@EnableConfigurationProperties
public class DataMigrateApplication {

    public static void main(String[] args) {
        try {
            ConfigurableApplicationContext context = SpringApplication.run(DataMigrateApplication.class, args);
            Environment env = context.getEnvironment();
            
            String protocol = "http";
            if (env.getProperty("server.ssl.key-store") != null) {
                protocol = "https";
            }
            
            String serverPort = env.getProperty("server.port", "8080");
            String contextPath = env.getProperty("server.servlet.context-path", "");
            String hostAddress = "localhost";
            
            try {
                hostAddress = InetAddress.getLocalHost().getHostAddress();
            } catch (UnknownHostException e) {
                log.warn("无法获取主机地址，使用localhost");
            }
            
            log.info("\n----------------------------------------------------------\n" +
                    "数据迁移管理系统启动成功！\n" +
                    "应用名称: {}\n" +
                    "访问地址: \n" +
                    "  本地访问: {}://localhost:{}{}\n" +
                    "  外部访问: {}://{}:{}{}\n" +
                    "  管理页面: {}://{}:{}{}/datamigrate/\n" +
                    "配置文件: {}\n" +
                    "----------------------------------------------------------",
                    env.getProperty("spring.application.name", "data-migrate-system"),
                    protocol, serverPort, contextPath,
                    protocol, hostAddress, serverPort, contextPath,
                    protocol, hostAddress, serverPort, contextPath,
                    env.getActiveProfiles().length == 0 ? "application.yml" : 
                        "application-" + String.join(",", env.getActiveProfiles()) + ".yml"
            );
            
        } catch (Exception e) {
            log.error("应用程序启动失败", e);
            System.exit(1);
        }
    }
}
